// Import Metro commands from the React Native CLI plugin (RN 0.68 compatible)
let commands = [];

try {
  // For RN 0.68, use @react-native-community/cli-plugin-metro
  const { commands: metroCommands } = require('@react-native-community/cli-plugin-metro');

  // 清理命令对象，移除可能导致验证错误的属性
  if (metroCommands && Array.isArray(metroCommands)) {
    commands = metroCommands.map(cmd => ({
      name: cmd.name,
      description: cmd.description,
      func: cmd.func,
      options: cmd.options
    }));
  }
} catch (error) {
  console.warn('Warning: @react-native-community/cli-plugin-metro not found, Metro commands may not be available');
}

module.exports = {
  commands,
};
