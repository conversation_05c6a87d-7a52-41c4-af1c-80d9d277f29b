/**
 * QRN Bundle命令
 * 整合了完整的QRN打包工作流，包括前置处理、bundle构建和后置处理
 *
 * <AUTHOR>
 * @description 统一的QRN打包工具入口
 */

// 获取 bundleCommand - 兼容 RN 0.68
let bundleCommand = null;
try {
  // 尝试从 @react-native-community/cli-plugin-metro 获取 (RN 0.68)
  const { commands } = require('@react-native-community/cli-plugin-metro');
  const originalBundleCmd = commands.find(cmd => cmd.name === 'bundle');

  if (originalBundleCmd) {
    // 清理命令对象，只保留标准属性，移除可能导致验证错误的属性
    bundleCommand = {
      name: originalBundleCmd.name,
      description: originalBundleCmd.description,
      func: originalBundleCmd.func,
      options: originalBundleCmd.options
    };
  }
} catch (error) {
  console.warn('Warning: Failed to load bundle command from @react-native-community/cli-plugin-metro:', error.message);
}
const path = require('path');
const chalk = require('chalk');

// 引入QRN特有的处理逻辑
const duties = require('../src/duties.js');
const utils = require('../src/utils');

// 从原始bundle命令获取配置
const originalBundleCommand = bundleCommand;
const bundleCommandLineArgs = originalBundleCommand ? originalBundleCommand.options : [];

// 如果没有找到 bundleCommand，提供一个基本的选项列表作为后备
const fallbackOptions = [
  {
    name: '--entry-file <path>',
    description: 'Path to the root JS file, either absolute or relative to JS root'
  },
  {
    name: '--platform <string>',
    description: 'Either "ios" or "android"',
    default: 'ios'
  },
  {
    name: '--bundle-output <string>',
    description: 'File name where to store the resulting bundle'
  },
  {
    name: '--sourcemap-output <string>',
    description: 'File name where to store the sourcemap file for resulting bundle'
  },
  {
    name: '--assets-dest <string>',
    description: 'Directory name where to store assets referenced in the bundle'
  },
  {
    name: '--dev [boolean]',
    description: 'If false, warnings are disabled and the bundle is minified',
    parse: (val) => val !== 'false',
    default: true
  }
];

const effectiveBundleCommandLineArgs = bundleCommandLineArgs.length > 0 ? bundleCommandLineArgs : fallbackOptions;

// 自定义参数定义
const customOptions = [
  {
    name: '--output-dir <string>',
    description: '打包产物输出目录',
    default: '.'
  },
  {
    name: '--bundle-type <string>',
    description: '打包类型: biz 或 platform',
    default: 'biz'
  },
  {
    name: '--build-tag <string>',
    description: 'Git构建标签'
  },
  {
    name: '--qpinfo <string>',
    description: 'QP信息配置',
    default: '{}'
  },
  {
    name: '--pltmap-dir <string>',
    description: '平台映射目录',
    default: '.'
  },
  {
    name: '--bumap-dir <string>',
    description: '业务映射目录',
    default: '.'
  },
  {
    name: '--pltmap-output <string>',
    description: '平台映射输出目录',
    default: '.'
  },
  {
    name: '--bumap-output <string>',
    description: '业务映射输出目录',
    default: '.'
  }
];

// 合并原生bundle命令参数和自定义参数
const allOptions = [...effectiveBundleCommandLineArgs, ...customOptions];

// 获取原始bundle命令的执行函数
const originalBundleFunc = originalBundleCommand ? originalBundleCommand.func : null;

/**
 * 参数处理工具类
 * 集中处理参数映射、默认值和兼容性问题
 */
class OptionsManager {
  // 参数映射表：命令行参数名 -> 内部属性名
  static paramMapping = {
    'bundle-type': 'bundleType',
    'build-tag': 'buildTag',
    'output-dir': 'outputDir',
    'qpinfo': 'qpInfo',
    'pltmap-dir': 'pltMapDir',
    'pltmap-output': 'pltMapOutput',
    'bumap-dir': 'buMapDir',
    'bumap-output': 'buMapOutput',
    'bundle-output': 'bundleOutput',
    'sourcemap-output': 'sourcemapOutput',
    'assets-dest': 'assetsDest'
  };

  // 默认值配置
  static defaultValues = {
    bundleType: 'biz',
    outputDir: '.',
    qpInfo: '{}',
    pltMapDir: '.',
    pltMapOutput: '.',
    buMapDir: '.',
    buMapOutput: '.',
    platform: 'ios'
  };

  /**
   * 规范化参数对象，处理兼容性问题
   * @param {Object} args - 原始参数对象
   * @returns {Object} - 规范化后的参数对象
   */
  static normalizeArgs(args) {
    const normalized = { ...args };
    
    // 处理参数映射和兼容性
    Object.entries(this.paramMapping).forEach(([kebabKey, camelKey]) => {
      // 如果驼峰格式存在，优先使用
      if (normalized[camelKey] === undefined && normalized[kebabKey] !== undefined) {
        normalized[camelKey] = normalized[kebabKey];
      }
      
      // 如果连字符格式存在，但驼峰格式不存在，则映射
      if (normalized[camelKey] === undefined && normalized[`--${kebabKey}`] !== undefined) {
        normalized[camelKey] = normalized[`--${kebabKey}`];
      }
    });
    
    // 应用默认值
    Object.entries(this.defaultValues).forEach(([key, defaultValue]) => {
      if (normalized[key] === undefined) {
        normalized[key] = defaultValue;
      }
    });
    
    return normalized;
  }

  /**
   * 创建预处理选项对象
   * @param {Object} args - 原始参数对象
   * @returns {Object} - 预处理选项对象
   */
  static createPreBundleOptions(args) {
    const normalized = this.normalizeArgs(args);
    
    return {
      __staticAssets: [],
      entryFile: normalized.entryFile,
      bundleType: normalized.bundleType,
      buildTag: normalized.buildTag,
      outputDir: normalized.outputDir,
      qpInfo: normalized.qpInfo,
      pltMapDir: normalized.pltMapDir,
      pltMapOutput: normalized.pltMapOutput,
      buMapDir: normalized.buMapDir,
      buMapOutput: normalized.buMapOutput,
      isRamBundle: normalized.isRamBundle,
      bundleOutput: normalized.bundleOutput,
      sourcemapOutput: normalized.sourcemapOutput,
      assetsDest: normalized.assetsDest,
      platform: normalized.platform
    };
  }

  /**
   * 创建后处理选项对象
   * @param {Object} preprocessedOptions - 预处理选项对象
   * @param {Object} args - 原始参数对象
   * @returns {Object} - 后处理选项对象
   */
  static createPostBundleOptions(preprocessedOptions, args) {
    const normalized = this.normalizeArgs(args);
    
    return {
      ...preprocessedOptions,
      __staticAssets: preprocessedOptions.__staticAssets || [],
      bundleOutput: normalized.bundleOutput,
      sourcemapOutput: normalized.sourcemapOutput,
      assetsDest: normalized.assetsDest
    };
  }
}

/**
 * 自定义bundle命令实现
 */
const customBundleCommand = {
  name: 'qrn-bundle',
  description: 'QRN bundle 命令，用于构建React Native bundle。',
  options: allOptions,

  func: async (argv, config, args) => {
    console.log(chalk.cyan('🚀 开始执行 QRN Bundle 命令...'));

    try {
      // 第一步：执行前置处理
      const preprocessedOptions = await executePreBundle(args);

      // 第二步：执行bundle构建
      const bundleResult = await executeBundleBuild(config, args, preprocessedOptions);

      // 第三步：执行后置处理
      await executePostBundle(preprocessedOptions, args);

      console.log(chalk.green('✅ QRN Bundle 构建完成'));
      return bundleResult;

    } catch (error) {
      console.error(chalk.red('❌ QRN Bundle 构建失败:'), error.message);
      utils.handleError(error);
    }
  }
};

/**
 * 执行前置处理
 */
async function executePreBundle(args) {
  console.log(chalk.blue('📋 执行前置处理...'));
  
  // 使用参数管理器创建规范化的选项对象
  const options = OptionsManager.createPreBundleOptions(args);
  
  return await duties.runBeforeBundle(options);
}

/**
 * 执行bundle构建
 */
async function executeBundleBuild(config, args, preprocessedOptions) {
  console.log(chalk.blue('⚡ 执行bundle构建...'));

  const { bundleType, hybridid, outputDir } = preprocessedOptions;
  const platform = args.platform || 'ios';

  // 设置全局环境变量
  global.QRN_PACK_ENV = {
    platform,
    bundleType,
    plt_map_dir: preprocessedOptions.pltMapDir,
    plt_map_output: preprocessedOptions.pltMapOutput,
    bu_map_dir: preprocessedOptions.buMapDir,
    bu_map_output: preprocessedOptions.buMapOutput,
    isRamBundle: preprocessedOptions.isRamBundle,
    buildTag: preprocessedOptions.buildTag
  };

  // 确定入口文件
  let entryFile = preprocessedOptions.hasReplaceEntry
    ? preprocessedOptions.entryFile
    : args.entryFile;

  if (bundleType === 'platform') {
    entryFile = path.join(__dirname, '..', 'platform.js');
  }

  if (!entryFile) {
    throw new Error('entryFile can\'t be empty');
  }

  // 动态生成输出路径
  if (!args.bundleOutput) {
    args.bundleOutput = path.join(outputDir, `${bundleType}.${hybridid}.${platform}.bundle`);
  }

  if (!args.sourcemapOutput) {
    args.sourcemapOutput = path.join(outputDir, `${bundleType}.${hybridid}.${platform}.map`);
  }

  if (!args.assetsDest) {
    args.assetsDest = outputDir;
  }

  // 设置默认配置
  if (!args.config) {
    args.config = './qrn.metro.config.js';
  }

  // 更新参数
  args.entryFile = entryFile;
  args.platform = platform;

  console.log(chalk.gray(`📦 Bundle输出: ${args.bundleOutput}`));
  console.log(chalk.gray(`🗺️  SourceMap输出: ${args.sourcemapOutput}`));
  console.log(chalk.gray(`🖼️  Assets输出: ${args.assetsDest}`));

  // 调用原始bundle命令
  if (!originalBundleFunc) {
    console.warn('警告: 无法找到原始bundle命令，尝试使用 Metro 直接构建...');

    // 如果没有原始 bundle 函数，尝试直接使用 Metro
    try {
      const { loadMetroConfig } = require('@react-native-community/cli-plugin-metro');
      const { buildBundleWithConfig } = require('@react-native-community/cli-plugin-metro');

      const metroConfig = await loadMetroConfig(config);
      return await buildBundleWithConfig(args, metroConfig);
    } catch (metroError) {
      throw new Error(`无法执行 bundle 构建: ${metroError.message}`);
    }
  }

  return await originalBundleFunc([], config, args);
}

/**
 * 执行后置处理
 */
async function executePostBundle(preprocessedOptions, args) {
  console.log(chalk.blue('🔧 执行后置处理...'));

  // 使用参数管理器创建规范化的选项对象
  const postOptions = OptionsManager.createPostBundleOptions(preprocessedOptions, args);

  return await duties.runAfterBundle(postOptions);
}

// 导出自定义bundle命令
module.exports = { customBundleCommand };
