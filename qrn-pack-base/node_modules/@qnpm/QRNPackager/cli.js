/**
 * Copyright (c) 2015-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 */
'use strict';

let defaultCli = require('@react-native-community/cli');

// 导出 QRN 命令供 react-native.config.js 使用
let commands = [];
try {
    const { commands: qrnCommands } = require('./qrn/commands');
    commands = qrnCommands;
} catch (error) {
    console.warn('Warning: Failed to load QRN commands:', error.message);
}

let cli = module.exports = {
    run: function (argvPassed) {
        let argv = argvPassed === cli.undefined ? process.argv : argvPassed
        qlog.debug('local-cli/cli.js run()\nargv:', argv)
        require('./qrn/src/cli.js').run(argv, defaultCli, cli)
    },
    // 导出命令供外部使用
    commands
};

if (require.main === module) {
    cli.run();
}
