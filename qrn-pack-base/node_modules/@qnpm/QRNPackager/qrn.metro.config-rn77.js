/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *  strict
 * @format
 */
"use strict";
const { getDefaultConfig } = require("@react-native/metro-config");
const { mergeConfig } = require("metro-config");
// TODO: 先不涉及鸿蒙 RN 77 的改造
const { createRN77Config, getLocalMetroConfig, getBuMetroConfig } = require("@qnpm/QRNPackager/metro-config-base");

// 合并所有配置
module.exports = (async () => {
  // 获取异步的本地配置
  const localConfig = await getLocalMetroConfig();
  // 获取异步的业务配置
  const buConfig = await getBuMetroConfig();

  return mergeConfig(
    getDefaultConfig(process.cwd()),
    createRN77Config(),
    localConfig,
    buConfig
  );
})();
