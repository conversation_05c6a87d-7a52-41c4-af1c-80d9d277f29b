{"version": 3, "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAOA", "sourcesContent": ["export {\n  Config,\n  ConfigLoadingContext,\n  MetroConfig,\n  getDefaultConfig,\n  default as loadMetroConfig,\n} from './tools/loadMetroConfig';\nexport {\n  default as commands,\n  buildBundleWithConfig,\n  CommandLineArgs,\n} from './commands';\n"]}