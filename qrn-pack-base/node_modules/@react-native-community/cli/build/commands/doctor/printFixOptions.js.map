{"version": 3, "sources": ["../../../src/commands/doctor/printFixOptions.ts"], "names": ["KEYS", "FIX_ALL_ISSUES", "FIX_ERRORS", "FIX_WARNINGS", "EXIT", "printOption", "option", "logger", "log", "printOptions", "chalk", "bold", "dim", "onKeyPress", "process", "stdin", "setRawMode", "resume", "setEncoding", "on"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAEA,MAAMA,IAAI,GAAG;AACXC,EAAAA,cAAc,EAAE,GADL;AAEXC,EAAAA,UAAU,EAAE,GAFD;AAGXC,EAAAA,YAAY,EAAE,GAHH;AAIXC,EAAAA,IAAI,EAAE;AAJK,CAAb;;;AAOA,MAAMC,WAAW,GAAIC,MAAD,IAAoBC,mBAAOC,GAAP,CAAY,WAAUF,MAAO,EAA7B,CAAxC;;AACA,MAAMG,YAAY,GAAG,MAAM;AACzBF,qBAAOC,GAAP;;AACAD,qBAAOC,GAAP,CAAWE,iBAAMC,IAAN,CAAW,OAAX,CAAX;;AACAN,EAAAA,WAAW,CACR,GAAEK,iBAAME,GAAN,CAAU,OAAV,CAAmB,IAAGZ,IAAI,CAACC,cAAe,IAAGS,iBAAME,GAAN,CAC9C,uBAD8C,CAE9C,EAHO,CAAX;AAKAP,EAAAA,WAAW,CACR,GAAEK,iBAAME,GAAN,CAAU,OAAV,CAAmB,IAAGZ,IAAI,CAACE,UAAW,IAAGQ,iBAAME,GAAN,CAC1C,uBAD0C,CAE1C,EAHO,CAAX;AAKAP,EAAAA,WAAW,CACR,GAAEK,iBAAME,GAAN,CAAU,OAAV,CAAmB,IAAGZ,IAAI,CAACG,YAAa,IAAGO,iBAAME,GAAN,CAC5C,yBAD4C,CAE5C,EAHO,CAAX;AAKAP,EAAAA,WAAW,CAAE,GAAEK,iBAAME,GAAN,CAAU,OAAV,CAAmB,UAASF,iBAAME,GAAN,CAAU,UAAV,CAAsB,EAAtD,CAAX;AACD,CAnBD;;eAsBe,CAAC;AAACC,EAAAA;AAAD,CAAD,KAA0D;AACvEJ,EAAAA,YAAY;;AAEZ,MAAIK,OAAO,CAACC,KAAR,CAAcC,UAAlB,EAA8B;AAC5BF,IAAAA,OAAO,CAACC,KAAR,CAAcC,UAAd,CAAyB,IAAzB;AACD;;AACDF,EAAAA,OAAO,CAACC,KAAR,CAAcE,MAAd;AACAH,EAAAA,OAAO,CAACC,KAAR,CAAcG,WAAd,CAA0B,MAA1B;AACAJ,EAAAA,OAAO,CAACC,KAAR,CAAcI,EAAd,CAAiB,MAAjB,EAAyBN,UAAzB;AACD,C", "sourcesContent": ["import chalk from 'chalk';\nimport {logger} from '@react-native-community/cli-tools';\n\nconst KEYS = {\n  FIX_ALL_ISSUES: 'f',\n  FIX_ERRORS: 'e',\n  FIX_WARNINGS: 'w',\n  EXIT: '\\r',\n};\n\nconst printOption = (option: string) => logger.log(` \\u203A ${option}`);\nconst printOptions = () => {\n  logger.log();\n  logger.log(chalk.bold('Usage'));\n  printOption(\n    `${chalk.dim('Press')} ${KEYS.FIX_ALL_ISSUES} ${chalk.dim(\n      'to try to fix issues.',\n    )}`,\n  );\n  printOption(\n    `${chalk.dim('Press')} ${KEYS.FIX_ERRORS} ${chalk.dim(\n      'to try to fix errors.',\n    )}`,\n  );\n  printOption(\n    `${chalk.dim('Press')} ${KEYS.FIX_WARNINGS} ${chalk.dim(\n      'to try to fix warnings.',\n    )}`,\n  );\n  printOption(`${chalk.dim('Press')} Enter ${chalk.dim('to exit.')}`);\n};\n\nexport {KEYS};\nexport default ({onKeyPress}: {onKeyPress: (...args: any[]) => void}) => {\n  printOptions();\n\n  if (process.stdin.setRawMode) {\n    process.stdin.setRawMode(true);\n  }\n  process.stdin.resume();\n  process.stdin.setEncoding('utf8');\n  process.stdin.on('data', onKeyPress);\n};\n"]}