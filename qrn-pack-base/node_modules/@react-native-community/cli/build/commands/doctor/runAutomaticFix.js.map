{"version": 3, "sources": ["../../../src/commands/doctor/runAutomaticFix.ts"], "names": ["AUTOMATIC_FIX_LEVELS", "healthchecks", "automaticFixLevel", "stats", "environmentInfo", "process", "stdout", "isTTY", "moveCursor", "clearScreenDown", "totalIssuesBasedOnFixLevel", "ALL_ISSUES", "errors", "warnings", "ERRORS", "WARNINGS", "issuesCount", "logger", "log", "chalk", "bold", "toString", "category", "healthchecksToRun", "filter", "healthcheck", "needsToBeFixed", "type", "HEALTHCHECK_TYPES", "ERROR", "WARNING", "length", "dim", "label", "healthcheckToRun", "spinner", "prefixText", "text", "start", "runAutomaticFix", "loader", "logManualInstallation", "error"], "mappings": ";;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAGA;;;;IAEYA,oB;;;WAAAA,oB;AAAAA,EAAAA,oB;AAAAA,EAAAA,oB;AAAAA,EAAAA,oB;GAAAA,oB,oCAAAA,oB;;AAiBG,wBAAgB;AAC7BC,EAAAA,YAD6B;AAE7BC,EAAAA,iBAF6B;AAG7BC,EAAAA,KAH6B;AAI7BC,EAAAA;AAJ6B,CAAhB,EAKS;AACtB;AACA,MAAIC,OAAO,CAACC,MAAR,CAAeC,KAAnB,EAA0B;AACxB;AACAF,IAAAA,OAAO,CAACC,MAAR,CAAeE,UAAf,CAA0B,CAA1B,EAA6B,CAAC,CAA9B,EAFwB,CAGxB;;AACAH,IAAAA,OAAO,CAACC,MAAR,CAAeG,eAAf;AACD;;AAED,QAAMC,0BAAiE,GAAG;AACxE,KAACV,oBAAoB,CAACW,UAAtB,GAAmCR,KAAK,CAACS,MAAN,GAAeT,KAAK,CAACU,QADgB;AAExE,KAACb,oBAAoB,CAACc,MAAtB,GAA+BX,KAAK,CAACS,MAFmC;AAGxE,KAACZ,oBAAoB,CAACe,QAAtB,GAAiCZ,KAAK,CAACU;AAHiC,GAA1E;AAKA,QAAMG,WAAW,GAAGN,0BAA0B,CAACR,iBAAD,CAA9C;;AAEAe,qBAAOC,GAAP,CACG,uBAAsBC,iBAAMC,IAAN,CAAWJ,WAAW,CAACK,QAAZ,EAAX,CAAmC,SACxDL,WAAW,GAAG,CAAd,GAAkB,GAAlB,GAAwB,EACzB,KAHH;;AAMA,OAAK,MAAMM,QAAX,IAAuBrB,YAAvB,EAAqC;AACnC,UAAMsB,iBAAiB,GAAGD,QAAQ,CAACrB,YAAT,CAAsBuB,MAAtB,CAA8BC,WAAD,IAAiB;AACtE,UAAIvB,iBAAiB,KAAKF,oBAAoB,CAACW,UAA/C,EAA2D;AACzD,eAAOc,WAAW,CAACC,cAAnB;AACD;;AAED,UAAIxB,iBAAiB,KAAKF,oBAAoB,CAACc,MAA/C,EAAuD;AACrD,eACEW,WAAW,CAACC,cAAZ,IACAD,WAAW,CAACE,IAAZ,KAAqBC,gCAAkBC,KAFzC;AAID;;AAED,UAAI3B,iBAAiB,KAAKF,oBAAoB,CAACe,QAA/C,EAAyD;AACvD,eACEU,WAAW,CAACC,cAAZ,IACAD,WAAW,CAACE,IAAZ,KAAqBC,gCAAkBE,OAFzC;AAID;;AAED;AACD,KApByB,CAA1B;;AAsBA,QAAI,CAACP,iBAAiB,CAACQ,MAAvB,EAA+B;AAC7B;AACD;;AAEDd,uBAAOC,GAAP,CAAY,KAAIC,iBAAMa,GAAN,CAAUV,QAAQ,CAACW,KAAnB,CAA0B,EAA1C;;AAEA,SAAK,MAAMC,gBAAX,IAA+BX,iBAA/B,EAAkD;AAChD,YAAMY,OAAO,GAAG,oBAAI;AAClBC,QAAAA,UAAU,EAAE,EADM;AAElBC,QAAAA,IAAI,EAAEH,gBAAgB,CAACD;AAFL,OAAJ,EAGbK,KAHa,EAAhB;;AAKA,UAAI;AACF,cAAMJ,gBAAgB,CAACK,eAAjB,CAAiC;AACrCC,UAAAA,MAAM,EAAEL,OAD6B;AAErCM,UAAAA,qBAAqB,EAArBA,6BAFqC;AAGrCrC,UAAAA;AAHqC,SAAjC,CAAN;AAKD,OAND,CAME,OAAOsC,KAAP,EAAc,CACd;AACD;AACF;AACF;AACF", "sourcesContent": ["import chalk from 'chalk';\nimport ora, {Ora} from 'ora';\nimport {logger} from '@react-native-community/cli-tools';\nimport {HEALTHCHECK_TYPES} from './healthchecks';\nimport {EnvironmentInfo} from '@react-native-community/cli-types';\nimport {HealthCheckCategoryResult} from './types';\nimport {logManualInstallation} from './healthchecks/common';\n\nexport enum AUTOMATIC_FIX_LEVELS {\n  ALL_ISSUES = 'ALL_ISSUES',\n  ERRORS = 'ERRORS',\n  WARNINGS = 'WARNINGS',\n}\n\ninterface RunAutomaticFixArgs {\n  healthchecks: HealthCheckCategoryResult[];\n  automaticFixLevel: AUTOMATIC_FIX_LEVELS;\n  stats: {\n    errors: number;\n    warnings: number;\n  };\n  loader: Ora;\n  environmentInfo: EnvironmentInfo;\n}\n\nexport default async function ({\n  healthchecks,\n  automaticFixLevel,\n  stats,\n  environmentInfo,\n}: RunAutomaticFixArgs) {\n  // Remove the fix options from screen\n  if (process.stdout.isTTY) {\n    // @ts-ignore\n    process.stdout.moveCursor(0, -6);\n    // @ts-ignore\n    process.stdout.clearScreenDown();\n  }\n\n  const totalIssuesBasedOnFixLevel: {[x in AUTOMATIC_FIX_LEVELS]: number} = {\n    [AUTOMATIC_FIX_LEVELS.ALL_ISSUES]: stats.errors + stats.warnings,\n    [AUTOMATIC_FIX_LEVELS.ERRORS]: stats.errors,\n    [AUTOMATIC_FIX_LEVELS.WARNINGS]: stats.warnings,\n  };\n  const issuesCount = totalIssuesBasedOnFixLevel[automaticFixLevel];\n\n  logger.log(\n    `\\nAttempting to fix ${chalk.bold(issuesCount.toString())} issue${\n      issuesCount > 1 ? 's' : ''\n    }...`,\n  );\n\n  for (const category of healthchecks) {\n    const healthchecksToRun = category.healthchecks.filter((healthcheck) => {\n      if (automaticFixLevel === AUTOMATIC_FIX_LEVELS.ALL_ISSUES) {\n        return healthcheck.needsToBeFixed;\n      }\n\n      if (automaticFixLevel === AUTOMATIC_FIX_LEVELS.ERRORS) {\n        return (\n          healthcheck.needsToBeFixed &&\n          healthcheck.type === HEALTHCHECK_TYPES.ERROR\n        );\n      }\n\n      if (automaticFixLevel === AUTOMATIC_FIX_LEVELS.WARNINGS) {\n        return (\n          healthcheck.needsToBeFixed &&\n          healthcheck.type === HEALTHCHECK_TYPES.WARNING\n        );\n      }\n\n      return;\n    });\n\n    if (!healthchecksToRun.length) {\n      continue;\n    }\n\n    logger.log(`\\n${chalk.dim(category.label)}`);\n\n    for (const healthcheckToRun of healthchecksToRun) {\n      const spinner = ora({\n        prefixText: '',\n        text: healthcheckToRun.label,\n      }).start();\n\n      try {\n        await healthcheckToRun.runAutomaticFix({\n          loader: spinner,\n          logManualInstallation,\n          environmentInfo,\n        });\n      } catch (error) {\n        // TODO: log the error in a meaningful way\n      }\n    }\n  }\n}\n"]}