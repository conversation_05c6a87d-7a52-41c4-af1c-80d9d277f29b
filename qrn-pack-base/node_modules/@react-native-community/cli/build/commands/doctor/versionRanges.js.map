{"version": 3, "sources": ["../../../src/commands/doctor/versionRanges.ts"], "names": ["NODE_JS", "YARN", "NPM", "WATCHMAN", "JAVA", "ANDROID_SDK", "ANDROID_NDK", "XCODE"], "mappings": ";;;;;;eAAe;AACb;AACAA,EAAAA,OAAO,EAAE,QAFI;AAGbC,EAAAA,IAAI,EAAE,WAHO;AAIbC,EAAAA,GAAG,EAAE,QAJQ;AAKbC,EAAAA,QAAQ,EAAE,KALG;AAMbC,EAAAA,IAAI,EAAE,eANO;AAOb;AACAC,EAAAA,WAAW,EAAE,SARA;AASbC,EAAAA,WAAW,EAAE,SATA;AAUb;AACAC,EAAAA,KAAK,EAAE;AAXM,C", "sourcesContent": ["export default {\n  // Common\n  NODE_JS: '>= 8.3',\n  YARN: '>= 1.10.x',\n  NPM: '>= 4.x',\n  WATCHMAN: '4.x',\n  JAVA: '1.8.x || >= 9',\n  // Android\n  ANDROID_SDK: '>= 26.x',\n  ANDROID_NDK: '>= 19.x',\n  // iOS\n  XCODE: '>= 10.x',\n};\n"]}