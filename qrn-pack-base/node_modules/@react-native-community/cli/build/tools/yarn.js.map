{"version": 3, "sources": ["../../src/tools/yarn.ts"], "names": ["getYarnVersionIfAvailable", "yarnVersion", "stdio", "toString", "trim", "error", "semver", "gte", "logger", "isProjectUsingYarn", "cwd", "findUp", "sync"], "mappings": ";;;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAXA;AACA;AACA;AACA;AACA;AACA;AACA;;AAOA;AACA;AACA;AACA;AACO,SAASA,yBAAT,GAAqC;AAC1C,MAAIC,WAAJ;;AACA,MAAI;AACF;AACAA,IAAAA,WAAW,GAAG,CACZ,+BAAS,gBAAT,EAA2B;AACzBC,MAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,MAAJ,EAAY,QAAZ;AADkB,KAA3B,EAEGC,QAFH,MAEiB,EAHL,EAIZC,IAJY,EAAd;AAKD,GAPD,CAOE,OAAOC,KAAP,EAAc;AACd,WAAO,IAAP;AACD,GAXyC,CAY1C;;;AACA,MAAI;AACF,QAAIC,kBAAOC,GAAP,CAAWN,WAAX,EAAwB,QAAxB,CAAJ,EAAuC;AACrC,aAAOA,WAAP;AACD;;AACD,WAAO,IAAP;AACD,GALD,CAKE,OAAOI,KAAP,EAAc;AACdG,uBAAOH,KAAP,CAAc,8BAA6BJ,WAAY,EAAvD;;AACA,WAAO,IAAP;AACD;AACF;AAED;AACA;AACA;;;AACO,SAASQ,kBAAT,CAA4BC,GAA5B,EAAyC;AAC9C,SAAOC,kBAAOC,IAAP,CAAY,WAAZ,EAAyB;AAACF,IAAAA;AAAD,GAAzB,CAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {execSync} from 'child_process';\nimport semver from 'semver';\nimport {logger} from '@react-native-community/cli-tools';\nimport findUp from 'find-up';\n\n/**\n * Use Yarn if available, it's much faster than the npm client.\n * Return the version of yarn installed on the system, null if yarn is not available.\n */\nexport function getYarnVersionIfAvailable() {\n  let yarnVersion;\n  try {\n    // execSync returns a Buffer -> convert to string\n    yarnVersion = (\n      execSync('yarn --version', {\n        stdio: [0, 'pipe', 'ignore'],\n      }).toString() || ''\n    ).trim();\n  } catch (error) {\n    return null;\n  }\n  // yarn < 0.16 has a 'missing manifest' bug\n  try {\n    if (semver.gte(yarnVersion, '0.16.0')) {\n      return yarnVersion;\n    }\n    return null;\n  } catch (error) {\n    logger.error(`Cannot parse yarn version: ${yarnVersion}`);\n    return null;\n  }\n}\n\n/**\n * Check if project is using Yarn (has `yarn.lock` in the tree)\n */\nexport function isProjectUsingYarn(cwd: string) {\n  return findUp.sync('yarn.lock', {cwd});\n}\n"]}