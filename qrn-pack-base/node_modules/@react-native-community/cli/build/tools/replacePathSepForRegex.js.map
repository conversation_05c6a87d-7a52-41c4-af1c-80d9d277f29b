{"version": 3, "sources": ["../../src/tools/replacePathSepForRegex.ts"], "names": ["replacePathSepForRegex", "string", "path", "sep", "replace", "_match", "_", "p2"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAEe,SAASA,sBAAT,CAAgCC,MAAhC,EAAgD;AAC7D,MAAIC,gBAAKC,GAAL,KAAa,IAAjB,EAAuB;AACrB,WAAOF,MAAM,CAACG,OAAP,CACL,oCADK,EAEL,CAACC,MAAD,EAASC,CAAT,EAAYC,EAAZ,KAAoBA,EAAE,IAAIA,EAAE,KAAK,IAAb,GAAoBA,EAAE,GAAG,MAAzB,GAAkC,MAFjD,CAAP;AAID;;AACD,SAAON,MAAP;AACD", "sourcesContent": ["import path from 'path';\n\nexport default function replacePathSepForRegex(string: string) {\n  if (path.sep === '\\\\') {\n    return string.replace(\n      /(\\/|(.)?\\\\(?![[\\]{}()*+?.^$|\\\\]))/g,\n      (_match, _, p2) => (p2 && p2 !== '\\\\' ? p2 + '\\\\\\\\' : '\\\\\\\\'),\n    );\n  }\n  return string;\n}\n"]}