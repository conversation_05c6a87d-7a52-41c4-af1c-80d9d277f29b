'use strict';

Object.defineProperty(exports, '__esModule', {
    value: true,
});
exports.default = void 0;

var _launchDefaultBrowser = _interopRequireDefault(require('./launchDefaultBrowser'));

function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : { default: obj };
}

/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 */
async function launchDebugger(url) {
    // QRN ADD
    const isWebIde = !!process.env.WEBIDE_USERNAME;
    if (isWebIde) {
    //    _cliTools().logger.warn(`\n\n 云环境无法自动开发浏览器, 请 command + 鼠标左键点击: \n 
    //  ${process.env.QRNDEBUGURL} \n\n `);
        console.log('\x1b[33m%s\x1b[0m', `\n\n 云环境无法自动开发浏览器, 请 command + 鼠标左键点击: \n 
        ${process.env.QRNDEBUGURL} \n\n `);
        return;
    }
    // QRN END

    return (0, _launchDefaultBrowser.default)(url);
}

var _default = launchDebugger;
exports.default = _default;

//# sourceMappingURL=launchDebugger.js.map
