{"name": "metro", "version": "0.67.0", "description": "🚇 The JavaScript bundler for React Native.", "main": "src/index.js", "bin": "src/cli.js", "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "dependencies": {"@babel/code-frame": "^7.0.0", "@babel/core": "^7.14.0", "@babel/generator": "^7.14.0", "@babel/parser": "^7.14.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.14.0", "@babel/types": "^7.0.0", "absolute-path": "^0.0.0", "accepts": "^1.3.7", "async": "^2.4.0", "chalk": "^4.0.0", "ci-info": "^2.0.0", "connect": "^3.6.5", "debug": "^2.2.0", "denodeify": "^1.2.1", "error-stack-parser": "^2.0.6", "fs-extra": "^1.0.0", "graceful-fs": "^4.1.3", "hermes-parser": "0.5.0", "image-size": "^0.6.0", "invariant": "^2.2.4", "jest-haste-map": "^27.3.1", "jest-worker": "^26.0.0", "lodash.throttle": "^4.1.1", "metro-babel-transformer": "0.67.0", "metro-cache": "0.67.0", "metro-cache-key": "0.67.0", "metro-config": "0.67.0", "metro-core": "0.67.0", "metro-hermes-compiler": "0.67.0", "metro-inspector-proxy": "0.67.0", "metro-minify-uglify": "0.67.0", "metro-react-native-babel-preset": "0.67.0", "metro-resolver": "0.67.0", "metro-runtime": "0.67.0", "metro-source-map": "0.67.0", "metro-symbolicate": "0.67.0", "metro-transform-plugins": "0.67.0", "metro-transform-worker": "0.67.0", "mime-types": "^2.1.27", "mkdirp": "^0.5.1", "node-fetch": "^2.2.0", "nullthrows": "^1.1.1", "rimraf": "^2.5.4", "serialize-error": "^2.1.0", "source-map": "^0.5.6", "strip-ansi": "^6.0.0", "temp": "0.8.3", "throat": "^5.0.0", "ws": "^7.5.1", "yargs": "^15.3.1"}, "devDependencies": {"@babel/plugin-transform-flow-strip-types": "^7.0.0", "acorn": "^5.1.2", "babel-jest": "^26.6.3", "dedent": "^0.7.0", "jest-snapshot": "^26.5.2", "metro-memory-fs": "0.67.0", "metro-react-native-babel-preset": "0.67.0", "metro-react-native-babel-transformer": "0.67.0", "stack-trace": "^0.0.10"}, "license": "MIT"}