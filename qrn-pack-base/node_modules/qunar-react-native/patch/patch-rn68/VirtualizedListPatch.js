// @flow

import React, { Component } from 'react';
import { VirtualizedList, View } from 'react-native';
const PropTypes = require('prop-types');
const invariant = require('fbjs/lib/invariant');

/**
 * hook VirtualizedList 的 _pushCells 方法
 */
VirtualizedList.prototype._pushCells = function(
    cells,
    stickyHeaderIndices,
    stickyIndicesFromProps,
    first,
    last,
    inversionStyle
) {
    const {
        ItemSeparatorComponent,
        data,
        getItem,
        getItemCount,
        keyExtractor,
        // QRN BEGIN
        // QAV统计，统计那些没有 section 的listView
        isSection
        // QRN END
    } = this.props;
    const stickyOffset = this.props.ListHeaderComponent ? 1 : 0;
    const end = getItemCount(data) - 1;
    let prevCellKey;
    last = Math.min(end, last);
    for (let ii = first; ii <= last; ii++) {
        const item = getItem(data, ii);
        invariant(item, 'No item for index ' + ii);
        const key = keyExtractor(item, ii);
        // QRN BEGIN
        // QAV 统计，每个 row 的 key，针对 sectionList 做判断
        let rowKey = isSection ? key : `r_${ii}`;
        // QRN END
        if (stickyIndicesFromProps.has(ii + stickyOffset)) {
            stickyHeaderIndices.push(cells.length);
        }
        cells.push(
            <CellRenderer
                ItemSeparatorComponent={ii < end ? ItemSeparatorComponent : undefined}
                cellKey={key}
                fillRateHelper={this._fillRateHelper}
                index={ii}
                inversionStyle={inversionStyle}
                item={item}
                // QRN BEGIN
                // QAV 统计
                key={rowKey}
                // QRN END
                prevCellKey={prevCellKey}
                onUpdateSeparators={this._onUpdateSeparators}
                onLayout={e => this._onCellLayout(e, key, ii)}
                onUnmount={this._onCellUnmount}
                parentProps={this.props}
                ref={ref => {
                    this._cellRefs[key] = ref;
                }}
            />
        );
        prevCellKey = key;
    }
};

/**
 * Copy CellRenderer from the react-native@0.61.3 source
 */
class CellRenderer extends Component {
    state = {
        separatorProps: {
            highlighted: false,
            leadingItem: this.props.item
        }
    };

    static childContextTypes = {
        virtualizedCell: PropTypes.shape({
            cellKey: PropTypes.string
        })
    };

    static getDerivedStateFromProps(props, prevState) {
        return {
            separatorProps: {
                ...prevState.separatorProps,
                leadingItem: props.item
            }
        };
    }

    getChildContext() {
        return {
            virtualizedCell: {
                cellKey: this.props.cellKey
            }
        };
    }

    // TODO: consider factoring separator stuff out of VirtualizedList into FlatList since it's not
    // reused by SectionList and we can keep VirtualizedList simpler.
    _separators = {
        highlight: () => {
            const { cellKey, prevCellKey } = this.props;
            this.props.onUpdateSeparators([cellKey, prevCellKey], {
                highlighted: true
            });
        },
        unhighlight: () => {
            const { cellKey, prevCellKey } = this.props;
            this.props.onUpdateSeparators([cellKey, prevCellKey], {
                highlighted: false
            });
        },
        updateProps: (select, newProps) => {
            const { cellKey, prevCellKey } = this.props;
            this.props.onUpdateSeparators([select === 'leading' ? prevCellKey : cellKey], newProps);
        }
    };

    updateSeparatorProps(newProps) {
        this.setState(state => ({
            separatorProps: { ...state.separatorProps, ...newProps }
        }));
    }

    componentWillUnmount() {
        this.props.onUnmount(this.props.cellKey);
    }

    _renderElement(renderItem, ListItemComponent, item, index) {
        if (renderItem && ListItemComponent) {
            console.warn(
                'VirtualizedList: Both ListItemComponent and renderItem props are present. ListItemComponent will take' +
                    ' precedence over renderItem.'
            );
        }

        if (ListItemComponent) {
            return React.createElement(ListItemComponent, {
                item,
                index,
                separators: this._separators
            });
        }

        if (renderItem) {
            return renderItem({
                item,
                index,
                separators: this._separators
            });
        }

        invariant(
            false,
            'VirtualizedList: Either ListItemComponent or renderItem props are required but none were found.'
        );
    }

    render() {
        const {
            CellRendererComponent,
            ItemSeparatorComponent,
            fillRateHelper,
            horizontal,
            item,
            index,
            inversionStyle,
            parentProps
        } = this.props;
        const { renderItem, getItemLayout, ListItemComponent } = parentProps;
        const element = this._renderElement(renderItem, ListItemComponent, item, index);

        const onLayout =
            /* $FlowFixMe(>=0.68.0 site=react_native_fb) This comment suppresses an
             * error found when Flow v0.68 was deployed. To see the error delete this
             * comment and run Flow. */
            getItemLayout && !parentProps.debug && !fillRateHelper.enabled()
                ? undefined
                : this.props.onLayout;
        // NOTE: that when this is a sticky header, `onLayout` will get automatically extracted and
        // called explicitly by `ScrollViewStickyHeader`.
        const itemSeparator = ItemSeparatorComponent && (
            <ItemSeparatorComponent {...this.state.separatorProps} />
        );
        const cellStyle = inversionStyle
            ? horizontal
                ? [inversionStyle]
                : [inversionStyle]
            : horizontal
            ? [inversionStyle]
            : inversionStyle;
        if (!CellRendererComponent) {
            return (
                /* $FlowFixMe(>=0.89.0 site=react_native_fb) This comment suppresses an
                 * error found when Flow v0.89 was deployed. To see the error, delete
                 * this comment and run Flow. */
                <View style={cellStyle} onLayout={onLayout}>
                    {element}
                    {itemSeparator}
                </View>
            );
        }
        return (
            <CellRendererComponent {...this.props} style={cellStyle} onLayout={onLayout}>
                {element}
                {itemSeparator}
            </CellRendererComponent>
        );
    }
}
