import React from 'react';
import { View } from 'react-native';
import ListView from 'deprecated-react-native-listview';
var cloneReferencedElement = require('react-clone-referenced-element');
var StaticRenderer = require('react-native/Libraries/Components/StaticRenderer.js');

var DEFAULT_SCROLL_CALLBACK_THROTTLE = 50;

ListView.prototype.render = function() {
    var bodyComponents = [];

    var dataSource = this.props.dataSource;
    var allRowIDs = dataSource.rowIdentities;
    var rowCount = 0;
    var stickySectionHeaderIndices = [];

    const { renderSectionHeader } = this.props;

    var header = this.props.renderHeader && this.props.renderHeader();
    var footer = this.props.renderFooter && this.props.renderFooter();
    var totalIndex = header ? 1 : 0;

    for (var sectionIdx = 0; sectionIdx < allRowIDs.length; sectionIdx++) {
        var sectionID = dataSource.sectionIdentities[sectionIdx];
        var rowIDs = allRowIDs[sectionIdx];
        if (rowIDs.length === 0) {
            if (this.props.enableEmptySections === undefined) {
                var warning = require('fbjs/lib/warning');
                warning(
                    false,
                    'In next release empty section headers will be rendered.' +
                        " In this release you can use 'enableEmptySections' flag to render empty section headers."
                );
                continue;
            } else {
                var invariant = require('fbjs/lib/invariant');
                invariant(
                    this.props.enableEmptySections,
                    "In next release 'enableEmptySections' flag will be deprecated, empty section headers will always be rendered." +
                        ' If empty section headers are not desirable their indices should be excluded from sectionIDs object.' +
                        " In this release 'enableEmptySections' may only have value 'true' to allow empty section headers rendering."
                );
            }
        }

        if (renderSectionHeader) {
            const element = renderSectionHeader(
                dataSource.getSectionHeaderData(sectionIdx),
                sectionID
            );
            if (element) {
                bodyComponents.push(
                    React.cloneElement(element, {
                        // QRN BEGIN
                        // 为了 QAV 统计，使用 sectionIdx 替换官方的 sectionID 作为key
                        key: 's_' + sectionIdx
                        // QRN END
                    })
                );
                if (this.props.stickySectionHeadersEnabled) {
                    stickySectionHeaderIndices.push(totalIndex);
                }
                totalIndex++;
            }
        }

        for (var rowIdx = 0; rowIdx < rowIDs.length; rowIdx++) {
            var rowID = rowIDs[rowIdx];
            var comboID = sectionID + '_' + rowID;
            var shouldUpdateRow =
                rowCount >= this._prevRenderedRowsCount &&
                dataSource.rowShouldUpdate(sectionIdx, rowIdx);
            var row = (
                <StaticRenderer
                    // QRN BEGIN
                    // 为了 QAV 统计，使用 sectionIdx + rowIdx 替换官方的 comboID 作为key
                    key={'r_' + sectionIdx + '_' + rowIdx}
                    // QRN END

                    shouldUpdate={!!shouldUpdateRow}
                    render={this.props.renderRow.bind(
                        null,
                        dataSource.getRowData(sectionIdx, rowIdx),
                        sectionID,
                        rowID,
                        this._onRowHighlighted
                    )}
                />
            );
            bodyComponents.push(row);
            totalIndex++;

            if (
                this.props.renderSeparator &&
                (rowIdx !== rowIDs.length - 1 || sectionIdx === allRowIDs.length - 1)
            ) {
                var adjacentRowHighlighted =
                    this.state.highlightedRow.sectionID === sectionID &&
                    (this.state.highlightedRow.rowID === rowID ||
                        this.state.highlightedRow.rowID === rowIDs[rowIdx + 1]);
                var separator = this.props.renderSeparator(
                    sectionID,
                    rowID,
                    adjacentRowHighlighted
                );
                if (separator) {
                    bodyComponents.push(<View key={'s_' + comboID}>{separator}</View>);
                    totalIndex++;
                }
            }
            if (++rowCount === this.state.curRenderedRowsCount) {
                break;
            }
        }
        if (rowCount >= this.state.curRenderedRowsCount) {
            break;
        }
    }

    var { renderScrollComponent, ...props } = this.props;
    if (!props.scrollEventThrottle) {
        props.scrollEventThrottle = DEFAULT_SCROLL_CALLBACK_THROTTLE;
    }
    if (props.removeClippedSubviews === undefined) {
        props.removeClippedSubviews = true;
    }
    Object.assign(props, {
        onScroll: this._onScroll,
        stickyHeaderIndices: this.props.stickyHeaderIndices.concat(stickySectionHeaderIndices),

        // Do not pass these events downstream to ScrollView since they will be
        // registered in ListView's own ScrollResponder.Mixin
        onKeyboardWillShow: undefined,
        onKeyboardWillHide: undefined,
        onKeyboardDidShow: undefined,
        onKeyboardDidHide: undefined
    });

    return cloneReferencedElement(
        renderScrollComponent(props),
        {
            ref: this._setScrollComponentRef,
            onContentSizeChange: this._onContentSizeChange,
            onLayout: this._onLayout,
            DEPRECATED_sendUpdatedChildFrames: typeof props.onChangeVisibleRows !== undefined
        },
        header,
        bodyComponents,
        footer
    );
};
