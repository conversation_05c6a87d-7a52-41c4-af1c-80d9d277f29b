import BatchedBridge from 'react-native/Libraries/BatchedBridge/BatchedBridge.js';
import TextInputState from 'react-native/Libraries/Components/TextInput/TextInputState.js'
// 新增方法，用来设置当前 focused 的 textInput
// 为了新增加的 QTextInput 专门增加的方法
TextInputState._setCurrentlyFocused = function (id) {
    TextInputState.focusTextInput(id);
};

BatchedBridge.registerCallableModule('TextInputState', TextInputState);
