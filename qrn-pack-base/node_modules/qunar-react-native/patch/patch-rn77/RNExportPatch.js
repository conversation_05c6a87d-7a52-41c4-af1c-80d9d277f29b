/**
 * for RN77
 * 为兼容旧版写法, 此文件将重新指向到新库上
 */
const rn = require('react-native');
import { NativeModules } from 'react-native';
const patchList = [
    {
        name: 'CheckBox',
        newLib: () => require('@react-native-community/checkbox').default,
    },
    {
        name: 'Picker',
        newLib: () => require('@react-native-picker/picker').Picker,
    },
    {
        name: 'PickerIOS',
        newLib: () => require('@react-native-picker/picker').PickerIOS,
    },
    {
        name: 'Slider',
        newLib: () => require('@react-native-community/slider').default,
    },
    {
        name: 'AsyncStorage',
        newLib: () => require('@react-native-async-storage/async-storage').default,
    },
    {
        name: 'Clipboard',
        newLib: () => require('@react-native-clipboard/clipboard').default,
    },
    {
        name: 'MaskedViewIOS',
        newLib: () => require('@react-native-masked-view/masked-view').default,
    },
    {
        name: 'SegmentedControlIOS',
        newLib: () => require('@react-native-segmented-control/segmented-control').default,
    },
    {
        name: 'ProgressViewIOS',
        newLib: () => require('@react-native-community/progress-view').ProgressView,
    },
    {
        name: 'ProgressBarAndroid',
        newLib: () => require('@react-native-community/progress-bar-android').ProgressBar,
    },
    {
        name: 'DatePickerIOS',
        newLib: () => require('@react-native-community/datetimepicker').default,
    },
];

const { qrn_version } = NativeModules.QRCTDeviceInfo.getConstants() || {}

if (qrn_version.replace('v', '').split('.')[0] >= 5) {
    for (const obj of patchList) {
        const value = obj.newLib()
        Object.defineProperty(rn, obj.name, {
            value
        });
    }
}
