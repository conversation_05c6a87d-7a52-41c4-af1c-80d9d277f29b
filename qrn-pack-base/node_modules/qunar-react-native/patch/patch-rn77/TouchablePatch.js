/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

'use strict';
import type { PressEvent } from 'react-native/Libraries/Types/CoreEventTypes';

const BoundingDimensions = require('react-native/Libraries/Components/Touchable/BoundingDimensions');
const Platform = require('react-native/Libraries/Utilities/Platform.ios');
const Position = require('react-native/Libraries/Components/Touchable/Position');
const ReactNative = require('react-native/Libraries/Renderer/shims/ReactNative');
const UIManager = require('react-native/Libraries/ReactNative/UIManager');
const HybridIdRegistry = require('../../extension/HybridIdRegistry/HybridIdRegistry.js');
const keyMirror = require('fbjs/lib/keyMirror');
const { Touchable } = require('react-native');
const QAV = require('react-native').NativeModules.QAV;
const AppRegistry = require('react-native').AppRegistry;
const SoundManager = require('react-native/Libraries/Components/Sound/SoundManager');

const extractSingleTouch = (nativeEvent) => {
    const touches = nativeEvent.touches;
    const changedTouches = nativeEvent.changedTouches;
    const hasTouches = touches && touches.length > 0;
    const hasChangedTouches = changedTouches && changedTouches.length > 0;

    return !hasTouches && hasChangedTouches ? changedTouches[0] : hasTouches ? touches[0] : nativeEvent;
};

/**
 * `Touchable`: Taps done right.
 *
 * You hook your `ResponderEventPlugin` events into `Touchable`. `Touchable`
 * will measure time/geometry and tells you when to give feedback to the user.
 *
 * ====================== Touchable Tutorial ===============================
 * The `Touchable` mixin helps you handle the "press" interaction. It analyzes
 * the geometry of elements, and observes when another responder (scroll view
 * etc) has stolen the touch lock. It notifies your component when it should
 * give feedback to the user. (bouncing/highlighting/unhighlighting).
 *
 * - When a touch was activated (typically you highlight)
 * - When a touch was deactivated (typically you unhighlight)
 * - When a touch was "pressed" - a touch ended while still within the geometry
 *   of the element, and no other element (like scroller) has "stolen" touch
 *   lock ("responder") (Typically you bounce the element).
 *
 * A good tap interaction isn't as simple as you might think. There should be a
 * slight delay before showing a highlight when starting a touch. If a
 * subsequent touch move exceeds the boundary of the element, it should
 * unhighlight, but if that same touch is brought back within the boundary, it
 * should rehighlight again. A touch can move in and out of that boundary
 * several times, each time toggling highlighting, but a "press" is only
 * triggered if that touch ends while within the element's boundary and no
 * scroller (or anything else) has stolen the lock on touches.
 *
 * To create a new type of component that handles interaction using the
 * `Touchable` mixin, do the following:
 *
 * - Initialize the `Touchable` state.
 *
 *   getInitialState: function() {
 *     return merge(this.touchableGetInitialState(), yourComponentState);
 *   }
 *
 * - Choose the rendered component who's touches should start the interactive
 *   sequence. On that rendered node, forward all `Touchable` responder
 *   handlers. You can choose any rendered node you like. Choose a node whose
 *   hit target you'd like to instigate the interaction sequence:
 *
 *   // In render function:
 *   return (
 *     <View
 *       onStartShouldSetResponder={this.touchableHandleStartShouldSetResponder}
 *       onResponderTerminationRequest={this.touchableHandleResponderTerminationRequest}
 *       onResponderGrant={this.touchableHandleResponderGrant}
 *       onResponderMove={this.touchableHandleResponderMove}
 *       onResponderRelease={this.touchableHandleResponderRelease}
 *       onResponderTerminate={this.touchableHandleResponderTerminate}>
 *       <View>
 *         Even though the hit detection/interactions are triggered by the
 *         wrapping (typically larger) node, we usually end up implementing
 *         custom logic that highlights this inner one.
 *       </View>
 *     </View>
 *   );
 *
 * - You may set up your own handlers for each of these events, so long as you
 *   also invoke the `touchable*` handlers inside of your custom handler.
 *
 * - Implement the handlers on your component class in order to provide
 *   feedback to the user. See documentation for each of these class methods
 *   that you should implement.
 *
 *   touchableHandlePress: function() {
 *      this.performBounceAnimation();  // or whatever you want to do.
 *   },
 *   touchableHandleActivePressIn: function() {
 *     this.beginHighlighting(...);  // Whatever you like to convey activation
 *   },
 *   touchableHandleActivePressOut: function() {
 *     this.endHighlighting(...);  // Whatever you like to convey deactivation
 *   },
 *
 * - There are more advanced methods you can implement (see documentation below):
 *   touchableGetHighlightDelayMS: function() {
 *     return 20;
 *   }
 *   // In practice, *always* use a predeclared constant (conserve memory).
 *   touchableGetPressRectOffset: function() {
 *     return {top: 20, left: 20, right: 20, bottom: 100};
 *   }
 */

/**
 * Touchable states.
 */

const States = keyMirror({
    NOT_RESPONDER: null, // Not the responder
    RESPONDER_INACTIVE_PRESS_IN: null, // Responder, inactive, in the `PressRect`
    RESPONDER_INACTIVE_PRESS_OUT: null, // Responder, inactive, out of `PressRect`
    RESPONDER_ACTIVE_PRESS_IN: null, // Responder, active, in the `PressRect`
    RESPONDER_ACTIVE_PRESS_OUT: null, // Responder, active, out of `PressRect`
    RESPONDER_ACTIVE_LONG_PRESS_IN: null, // Responder, active, in the `PressRect`, after long press threshold
    RESPONDER_ACTIVE_LONG_PRESS_OUT: null, // Responder, active, out of `PressRect`, after long press threshold
    ERROR: null,
});

type State =
    | typeof States.NOT_RESPONDER
    | typeof States.RESPONDER_INACTIVE_PRESS_IN
    | typeof States.RESPONDER_INACTIVE_PRESS_OUT
    | typeof States.RESPONDER_ACTIVE_PRESS_IN
    | typeof States.RESPONDER_ACTIVE_PRESS_OUT
    | typeof States.RESPONDER_ACTIVE_LONG_PRESS_IN
    | typeof States.RESPONDER_ACTIVE_LONG_PRESS_OUT
    | typeof States.ERROR;

/*
 * Quick lookup map for states that are considered to be "active"
 */

const baseStatesConditions = {
    NOT_RESPONDER: false,
    RESPONDER_INACTIVE_PRESS_IN: false,
    RESPONDER_INACTIVE_PRESS_OUT: false,
    RESPONDER_ACTIVE_PRESS_IN: false,
    RESPONDER_ACTIVE_PRESS_OUT: false,
    RESPONDER_ACTIVE_LONG_PRESS_IN: false,
    RESPONDER_ACTIVE_LONG_PRESS_OUT: false,
    ERROR: false,
};

const IsActive = {
    ...baseStatesConditions,
    RESPONDER_ACTIVE_PRESS_OUT: true,
    RESPONDER_ACTIVE_PRESS_IN: true,
};

/**
 * Quick lookup for states that are considered to be "pressing" and are
 * therefore eligible to result in a "selection" if the press stops.
 */
const IsPressingIn = {
    ...baseStatesConditions,
    RESPONDER_INACTIVE_PRESS_IN: true,
    RESPONDER_ACTIVE_PRESS_IN: true,
    RESPONDER_ACTIVE_LONG_PRESS_IN: true,
};

const IsLongPressingIn = {
    ...baseStatesConditions,
    RESPONDER_ACTIVE_LONG_PRESS_IN: true,
};

/**
 * Inputs to the state machine.
 */
const Signals = keyMirror({
    DELAY: null,
    RESPONDER_GRANT: null,
    RESPONDER_RELEASE: null,
    RESPONDER_TERMINATED: null,
    ENTER_PRESS_RECT: null,
    LEAVE_PRESS_RECT: null,
    LONG_PRESS_DETECTED: null,
});

type Signal =
    | typeof Signals.DELAY
    | typeof Signals.RESPONDER_GRANT
    | typeof Signals.RESPONDER_RELEASE
    | typeof Signals.RESPONDER_TERMINATED
    | typeof Signals.ENTER_PRESS_RECT
    | typeof Signals.LEAVE_PRESS_RECT
    | typeof Signals.LONG_PRESS_DETECTED;

/**
 * Mapping from States x Signals => States
 */
const Transitions = {
    NOT_RESPONDER: {
        DELAY: States.ERROR,
        RESPONDER_GRANT: States.RESPONDER_INACTIVE_PRESS_IN,
        RESPONDER_RELEASE: States.ERROR,
        RESPONDER_TERMINATED: States.ERROR,
        ENTER_PRESS_RECT: States.ERROR,
        LEAVE_PRESS_RECT: States.ERROR,
        LONG_PRESS_DETECTED: States.ERROR,
    },
    RESPONDER_INACTIVE_PRESS_IN: {
        DELAY: States.RESPONDER_ACTIVE_PRESS_IN,
        RESPONDER_GRANT: States.ERROR,
        RESPONDER_RELEASE: States.NOT_RESPONDER,
        RESPONDER_TERMINATED: States.NOT_RESPONDER,
        ENTER_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_IN,
        LEAVE_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_OUT,
        LONG_PRESS_DETECTED: States.ERROR,
    },
    RESPONDER_INACTIVE_PRESS_OUT: {
        DELAY: States.RESPONDER_ACTIVE_PRESS_OUT,
        RESPONDER_GRANT: States.ERROR,
        RESPONDER_RELEASE: States.NOT_RESPONDER,
        RESPONDER_TERMINATED: States.NOT_RESPONDER,
        ENTER_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_IN,
        LEAVE_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_OUT,
        LONG_PRESS_DETECTED: States.ERROR,
    },
    RESPONDER_ACTIVE_PRESS_IN: {
        DELAY: States.ERROR,
        RESPONDER_GRANT: States.ERROR,
        RESPONDER_RELEASE: States.NOT_RESPONDER,
        RESPONDER_TERMINATED: States.NOT_RESPONDER,
        ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_IN,
        LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_OUT,
        LONG_PRESS_DETECTED: States.RESPONDER_ACTIVE_LONG_PRESS_IN,
    },
    RESPONDER_ACTIVE_PRESS_OUT: {
        DELAY: States.ERROR,
        RESPONDER_GRANT: States.ERROR,
        RESPONDER_RELEASE: States.NOT_RESPONDER,
        RESPONDER_TERMINATED: States.NOT_RESPONDER,
        ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_IN,
        LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_OUT,
        LONG_PRESS_DETECTED: States.ERROR,
    },
    RESPONDER_ACTIVE_LONG_PRESS_IN: {
        DELAY: States.ERROR,
        RESPONDER_GRANT: States.ERROR,
        RESPONDER_RELEASE: States.NOT_RESPONDER,
        RESPONDER_TERMINATED: States.NOT_RESPONDER,
        ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_IN,
        LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_OUT,
        LONG_PRESS_DETECTED: States.RESPONDER_ACTIVE_LONG_PRESS_IN,
    },
    RESPONDER_ACTIVE_LONG_PRESS_OUT: {
        DELAY: States.ERROR,
        RESPONDER_GRANT: States.ERROR,
        RESPONDER_RELEASE: States.NOT_RESPONDER,
        RESPONDER_TERMINATED: States.NOT_RESPONDER,
        ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_IN,
        LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_OUT,
        LONG_PRESS_DETECTED: States.ERROR,
    },
    error: {
        DELAY: States.NOT_RESPONDER,
        RESPONDER_GRANT: States.RESPONDER_INACTIVE_PRESS_IN,
        RESPONDER_RELEASE: States.NOT_RESPONDER,
        RESPONDER_TERMINATED: States.NOT_RESPONDER,
        ENTER_PRESS_RECT: States.NOT_RESPONDER,
        LEAVE_PRESS_RECT: States.NOT_RESPONDER,
        LONG_PRESS_DETECTED: States.NOT_RESPONDER,
    },
};

// ==== Typical Constants for integrating into UI components ====
// var HIT_EXPAND_PX = 20;
// var HIT_VERT_OFFSET_PX = 10;
const HIGHLIGHT_DELAY_MS = 130;

const PRESS_EXPAND_PX = 20;

const LONG_PRESS_THRESHOLD = 500;

const LONG_PRESS_DELAY_MS = LONG_PRESS_THRESHOLD - HIGHLIGHT_DELAY_MS;

const LONG_PRESS_ALLOWED_MOVEMENT = 10;

// Default amount "active" region protrudes beyond box

/**
 * By convention, methods prefixed with underscores are meant to be @private,
 * and not @protected. Mixers shouldn't access them - not even to provide them
 * as callback handlers.
 *
 *
 * ========== Geometry =========
 * `Touchable` only assumes that there exists a `HitRect` node. The `PressRect`
 * is an abstract box that is extended beyond the `HitRect`.
 *
 *  +--------------------------+
 *  |                          | - "Start" events in `HitRect` cause `HitRect`
 *  |  +--------------------+  |   to become the responder.
 *  |  |  +--------------+  |  | - `HitRect` is typically expanded around
 *  |  |  |              |  |  |   the `VisualRect`, but shifted downward.
 *  |  |  |  VisualRect  |  |  | - After pressing down, after some delay,
 *  |  |  |              |  |  |   and before letting up, the Visual React
 *  |  |  +--------------+  |  |   will become "active". This makes it eligible
 *  |  |     HitRect        |  |   for being highlighted (so long as the
 *  |  +--------------------+  |   press remains in the `PressRect`).
 *  |        PressRect     o   |
 *  +----------------------|---+
 *           Out Region    |
 *                         +-----+ This gap between the `HitRect` and
 *                                 `PressRect` allows a touch to move far away
 *                                 from the original hit rect, and remain
 *                                 highlighted, and eligible for a "Press".
 *                                 Customize this via
 *                                 `touchableGetPressRectOffset()`.
 *
 *
 *
 * ======= State Machine =======
 *
 * +-------------+ <---+ RESPONDER_RELEASE
 * |NOT_RESPONDER|
 * +-------------+ <---+ RESPONDER_TERMINATED
 *     +
 *     | RESPONDER_GRANT (HitRect)
 *     v
 * +---------------------------+  DELAY   +-------------------------+  T + DELAY     +------------------------------+
 * |RESPONDER_INACTIVE_PRESS_IN|+-------->|RESPONDER_ACTIVE_PRESS_IN| +------------> |RESPONDER_ACTIVE_LONG_PRESS_IN|
 * +---------------------------+          +-------------------------+                +------------------------------+
 *     +            ^                         +           ^                                 +           ^
 *     |LEAVE_      |ENTER_                   |LEAVE_     |ENTER_                           |LEAVE_     |ENTER_
 *     |PRESS_RECT  |PRESS_RECT               |PRESS_RECT |PRESS_RECT                       |PRESS_RECT |PRESS_RECT
 *     |            |                         |           |                                 |           |
 *     v            +                         v           +                                 v           +
 * +----------------------------+  DELAY  +--------------------------+               +-------------------------------+
 * |RESPONDER_INACTIVE_PRESS_OUT|+------->|RESPONDER_ACTIVE_PRESS_OUT|               |RESPONDER_ACTIVE_LONG_PRESS_OUT|
 * +----------------------------+         +--------------------------+               +-------------------------------+
 *
 * T + DELAY => LONG_PRESS_DELAY_MS + DELAY
 *
 * Not drawn are the side effects of each transition. The most important side
 * effect is the `touchableHandlePress` abstract method invocation that occurs
 * when a responder is released while in either of the "Press" states.
 *
 * The other important side effects are the highlight abstract method
 * invocations (internal callbacks) to be implemented by the mixer.
 *
 *
 * @lends Touchable.prototype
 */
const TouchableMixin = {
    componentDidMount: function () {
        if (!Platform.isTV) {
            return;
        }
    },

    /**
     * Clear all timeouts on unmount
     */
    componentWillUnmount: function () {
        this.touchableDelayTimeout && clearTimeout(this.touchableDelayTimeout);
        this.longPressDelayTimeout && clearTimeout(this.longPressDelayTimeout);
        this.pressOutDelayTimeout && clearTimeout(this.pressOutDelayTimeout);
    },

    /**
     * It's prefer that mixins determine state in this way, having the class
     * explicitly mix the state in the one and only `getInitialState` method.
     *
     * @return {object} State object to be placed inside of
     * `this.state.touchable`.
     */
    touchableGetInitialState: function (): $TEMPORARY$object<{|
        touchable: $TEMPORARY$object<{| responderID: null, touchState: void |}>,
    |}> {
        return {
            touchable: { touchState: undefined, responderID: null },
        };
    },

    // ==== Hooks to Gesture Responder system ====
    /**
     * Must return true if embedded in a native platform scroll view.
     */
    touchableHandleResponderTerminationRequest: function (): any {
        return !this.props.rejectResponderTermination;
    },

    /**
     * Must return true to start the process of `Touchable`.
     */
    touchableHandleStartShouldSetResponder: function (): any {
        return !this.props.disabled;
    },

    /**
     * Return true to cancel press on long press.
     */
    touchableLongPressCancelsPress: function (): boolean {
        return true;
    },

    /**
     * Place as callback for a DOM element's `onResponderGrant` event.
     * @param {SyntheticEvent} e Synthetic event from event system.
     *
     */
    touchableHandleResponderGrant: function (e: PressEvent) {
        const dispatchID = e.currentTarget;
        // Since e is used in a callback invoked on another event loop
        // (as in setTimeout etc), we need to call e.persist() on the
        // event to make sure it doesn't get reused in the event object pool.
        e.persist();

        this.pressOutDelayTimeout && clearTimeout(this.pressOutDelayTimeout);
        this.pressOutDelayTimeout = null;

        this.state.touchable.touchState = States.NOT_RESPONDER;
        this.state.touchable.responderID = dispatchID;
        this._receiveSignal(Signals.RESPONDER_GRANT, e);
        let delayMS =
            this.touchableGetHighlightDelayMS !== undefined
                ? Math.max(this.touchableGetHighlightDelayMS(), 0)
                : HIGHLIGHT_DELAY_MS;
        delayMS = isNaN(delayMS) ? HIGHLIGHT_DELAY_MS : delayMS;
        if (delayMS !== 0) {
            this.touchableDelayTimeout = setTimeout(this._handleDelay.bind(this, e), delayMS);
        } else {
            this._handleDelay(e);
        }

        let longDelayMS =
            this.touchableGetLongPressDelayMS !== undefined
                ? Math.max(this.touchableGetLongPressDelayMS(), 10)
                : LONG_PRESS_DELAY_MS;
        longDelayMS = isNaN(longDelayMS) ? LONG_PRESS_DELAY_MS : longDelayMS;
        this.longPressDelayTimeout = setTimeout(this._handleLongDelay.bind(this, e), longDelayMS + delayMS);
    },

    /**
     * Place as callback for a DOM element's `onResponderRelease` event.
     */
    touchableHandleResponderRelease: function (e: PressEvent) {
        this.pressInLocation = null;
        this._receiveSignal(Signals.RESPONDER_RELEASE, e);
    },

    /**
     * Place as callback for a DOM element's `onResponderTerminate` event.
     */
    touchableHandleResponderTerminate: function (e: PressEvent) {
        this.pressInLocation = null;
        this._receiveSignal(Signals.RESPONDER_TERMINATED, e);
    },

    /**
     * Place as callback for a DOM element's `onResponderMove` event.
     */
    touchableHandleResponderMove: function (e: PressEvent) {
        // Measurement may not have returned yet.
        if (!this.state.touchable.positionOnActivate) {
            return;
        }

        const positionOnActivate = this.state.touchable.positionOnActivate;
        const dimensionsOnActivate = this.state.touchable.dimensionsOnActivate;
        const pressRectOffset = this.touchableGetPressRectOffset
            ? this.touchableGetPressRectOffset()
            : {
                  left: PRESS_EXPAND_PX,
                  right: PRESS_EXPAND_PX,
                  top: PRESS_EXPAND_PX,
                  bottom: PRESS_EXPAND_PX,
              };

        let pressExpandLeft = pressRectOffset.left;
        let pressExpandTop = pressRectOffset.top;
        let pressExpandRight = pressRectOffset.right;
        let pressExpandBottom = pressRectOffset.bottom;

        const hitSlop = this.touchableGetHitSlop ? this.touchableGetHitSlop() : null;

        if (hitSlop) {
            pressExpandLeft += hitSlop.left || 0;
            pressExpandTop += hitSlop.top || 0;
            pressExpandRight += hitSlop.right || 0;
            pressExpandBottom += hitSlop.bottom || 0;
        }

        const touch = extractSingleTouch(e.nativeEvent);
        const pageX = touch && touch.pageX;
        const pageY = touch && touch.pageY;

        if (this.pressInLocation) {
            const movedDistance = this._getDistanceBetweenPoints(
                pageX,
                pageY,
                this.pressInLocation.pageX,
                this.pressInLocation.pageY
            );
            if (movedDistance > LONG_PRESS_ALLOWED_MOVEMENT) {
                this._cancelLongPressDelayTimeout();
            }
        }

        const isTouchWithinActive =
            pageX > positionOnActivate.left - pressExpandLeft &&
            pageY > positionOnActivate.top - pressExpandTop &&
            pageX < positionOnActivate.left + dimensionsOnActivate.width + pressExpandRight &&
            pageY < positionOnActivate.top + dimensionsOnActivate.height + pressExpandBottom;
        if (isTouchWithinActive) {
            const prevState = this.state.touchable.touchState;
            this._receiveSignal(Signals.ENTER_PRESS_RECT, e);
            const curState = this.state.touchable.touchState;
            if (curState === States.RESPONDER_INACTIVE_PRESS_IN && prevState !== States.RESPONDER_INACTIVE_PRESS_IN) {
                // fix for t7967420
                this._cancelLongPressDelayTimeout();
            }
        } else {
            this._cancelLongPressDelayTimeout();
            this._receiveSignal(Signals.LEAVE_PRESS_RECT, e);
        }
    },

    /**
     * Invoked when the item receives focus. Mixers might override this to
     * visually distinguish the `VisualRect` so that the user knows that it
     * currently has the focus. Most platforms only support a single element being
     * focused at a time, in which case there may have been a previously focused
     * element that was blurred just prior to this. This can be overridden when
     * using `Touchable.Mixin.withoutDefaultFocusAndBlur`.
     */
    touchableHandleFocus: function (e: Event) {
        this.props.onFocus && this.props.onFocus(e);
    },

    /**
     * Invoked when the item loses focus. Mixers might override this to
     * visually distinguish the `VisualRect` so that the user knows that it
     * no longer has focus. Most platforms only support a single element being
     * focused at a time, in which case the focus may have moved to another.
     * This can be overridden when using
     * `Touchable.Mixin.withoutDefaultFocusAndBlur`.
     */
    touchableHandleBlur: function (e: Event) {
        this.props.onBlur && this.props.onBlur(e);
    },

    // ==== Abstract Application Callbacks ====

    /**
     * Invoked when the item should be highlighted. Mixers should implement this
     * to visually distinguish the `VisualRect` so that the user knows that
     * releasing a touch will result in a "selection" (analog to click).
     *
     * @abstract
     * touchableHandleActivePressIn: function,
     */

    /**
     * Invoked when the item is "active" (in that it is still eligible to become
     * a "select") but the touch has left the `PressRect`. Usually the mixer will
     * want to unhighlight the `VisualRect`. If the user (while pressing) moves
     * back into the `PressRect` `touchableHandleActivePressIn` will be invoked
     * again and the mixer should probably highlight the `VisualRect` again. This
     * event will not fire on an `touchEnd/mouseUp` event, only move events while
     * the user is depressing the mouse/touch.
     *
     * @abstract
     * touchableHandleActivePressOut: function
     */

    /**
     * Invoked when the item is "selected" - meaning the interaction ended by
     * letting up while the item was either in the state
     * `RESPONDER_ACTIVE_PRESS_IN` or `RESPONDER_INACTIVE_PRESS_IN`.
     *
     * @abstract
     * touchableHandlePress: function
     */

    /**
     * Invoked when the item is long pressed - meaning the interaction ended by
     * letting up while the item was in `RESPONDER_ACTIVE_LONG_PRESS_IN`. If
     * `touchableHandleLongPress` is *not* provided, `touchableHandlePress` will
     * be called as it normally is. If `touchableHandleLongPress` is provided, by
     * default any `touchableHandlePress` callback will not be invoked. To
     * override this default behavior, override `touchableLongPressCancelsPress`
     * to return false. As a result, `touchableHandlePress` will be called when
     * lifting up, even if `touchableHandleLongPress` has also been called.
     *
     * @abstract
     * touchableHandleLongPress: function
     */

    /**
     * Returns the number of millis to wait before triggering a highlight.
     *
     * @abstract
     * touchableGetHighlightDelayMS: function
     */

    /**
     * Returns the amount to extend the `HitRect` into the `PressRect`. Positive
     * numbers mean the size expands outwards.
     *
     * @abstract
     * touchableGetPressRectOffset: function
     */

    // ==== Internal Logic ====

    /**
     * Measures the `HitRect` node on activation. The Bounding rectangle is with
     * respect to viewport - not page, so adding the `pageXOffset/pageYOffset`
     * should result in points that are in the same coordinate system as an
     * event's `globalX/globalY` data values.
     *
     * - Consider caching this for the lifetime of the component, or possibly
     *   being able to share this cache between any `ScrollMap` view.
     *
     * @sideeffects
     * @private
     */
    _remeasureMetricsOnActivation: function () {
        const responderID = this.state.touchable.responderID;
        if (responderID == null) {
            return;
        }

        if (typeof responderID === 'number') {
            UIManager.measure(responderID, this._handleQueryLayout);
        } else {
            responderID.measure(this._handleQueryLayout);
        }
    },

    _handleQueryLayout: function (l: number, t: number, w: number, h: number, globalX: number, globalY: number) {
        //don't do anything UIManager failed to measure node
        if (!l && !t && !w && !h && !globalX && !globalY) {
            return;
        }
        this.state.touchable.positionOnActivate && Position.release(this.state.touchable.positionOnActivate);
        this.state.touchable.dimensionsOnActivate &&
            BoundingDimensions.release(this.state.touchable.dimensionsOnActivate);
        this.state.touchable.positionOnActivate = Position.getPooled(globalX, globalY);
        this.state.touchable.dimensionsOnActivate = BoundingDimensions.getPooled(w, h);
    },

    _handleDelay: function (e: PressEvent) {
        this.touchableDelayTimeout = null;
        this._receiveSignal(Signals.DELAY, e);
    },

    _handleLongDelay: function (e: PressEvent) {
        this.longPressDelayTimeout = null;
        const curState = this.state.touchable.touchState;
        if (curState === States.RESPONDER_ACTIVE_PRESS_IN || curState === States.RESPONDER_ACTIVE_LONG_PRESS_IN) {
            this._receiveSignal(Signals.LONG_PRESS_DETECTED, e);
        }
    },

    /**
     * Receives a state machine signal, performs side effects of the transition
     * and stores the new state. Validates the transition as well.
     *
     * @param {Signals} signal State machine signal.
     * @throws Error if invalid state transition or unrecognized signal.
     * @sideeffects
     */
    _receiveSignal: function (signal: Signal, e: PressEvent) {
        const responderID = this.state.touchable.responderID;
        const curState = this.state.touchable.touchState;
        const nextState = Transitions[curState] && Transitions[curState][signal];
        if (!responderID && signal === Signals.RESPONDER_RELEASE) {
            return;
        }
        if (!nextState) {
            throw new Error(
                'Unrecognized signal `' +
                    signal +
                    '` or state `' +
                    curState +
                    '` for Touchable responder `' +
                    typeof this.state.touchable.responderID ===
                'number'
                    ? this.state.touchable.responderID
                    : 'host component' + '`'
            );
        }
        if (nextState === States.ERROR) {
            throw new Error(
                'Touchable cannot transition from `' +
                    curState +
                    '` to `' +
                    signal +
                    '` for responder `' +
                    typeof this.state.touchable.responderID ===
                'number'
                    ? this.state.touchable.responderID
                    : '<<host component>>' + '`'
            );
        }
        if (curState !== nextState) {
            this._performSideEffectsForTransition(curState, nextState, signal, e);
            this.state.touchable.touchState = nextState;
        }
    },

    _cancelLongPressDelayTimeout: function () {
        this.longPressDelayTimeout && clearTimeout(this.longPressDelayTimeout);
        this.longPressDelayTimeout = null;
    },

    _isHighlight: function (state: State): boolean {
        return state === States.RESPONDER_ACTIVE_PRESS_IN || state === States.RESPONDER_ACTIVE_LONG_PRESS_IN;
    },

    _savePressInLocation: function (e: PressEvent) {
        const touch = extractSingleTouch(e.nativeEvent);
        const pageX = touch && touch.pageX;
        const pageY = touch && touch.pageY;
        const locationX = touch && touch.locationX;
        const locationY = touch && touch.locationY;
        this.pressInLocation = { pageX, pageY, locationX, locationY };
    },

    _getDistanceBetweenPoints: function (aX: number, aY: number, bX: number, bY: number): number {
        const deltaX = aX - bX;
        const deltaY = aY - bY;
        return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    },

    /**
     * Will perform a transition between touchable states, and identify any
     * highlighting or unhighlighting that must be performed for this particular
     * transition.
     *
     * @param {States} curState Current Touchable state.
     * @param {States} nextState Next Touchable state.
     * @param {Signal} signal Signal that triggered the transition.
     * @param {Event} e Native event.
     * @sideeffects
     */
    _performSideEffectsForTransition: function (curState: State, nextState: State, signal: Signal, e: PressEvent) {
        const curIsHighlight = this._isHighlight(curState);
        const newIsHighlight = this._isHighlight(nextState);

        const isFinalSignal = signal === Signals.RESPONDER_TERMINATED || signal === Signals.RESPONDER_RELEASE;

        if (isFinalSignal) {
            this._cancelLongPressDelayTimeout();
        }

        const isInitialTransition =
            curState === States.NOT_RESPONDER && nextState === States.RESPONDER_INACTIVE_PRESS_IN;

        const isActiveTransition = !IsActive[curState] && IsActive[nextState];
        if (isInitialTransition || isActiveTransition) {
            this._remeasureMetricsOnActivation();
        }

        // QRN BEGIN 处理 long press 的时候添加了QAV记录和获取的逻辑
        if (IsPressingIn[curState] && signal === Signals.LONG_PRESS_DETECTED && this.touchableHandleLongPress) {
            if (e.nativeEvent && e.nativeEvent.fromQAV) {
                try {
                    this._getTouchEventXPath(e, 'longPress');
                } catch (e) {}
            } else {
                try {
                    this._recordTouchEvent(e, 'longPress');
                } catch (e) {}

                this.touchableHandleLongPress(e);
            }
        }

        // del
        // if (IsPressingIn[curState] && signal === Signals.LONG_PRESS_DETECTED) {
        //     this.touchableHandleLongPress && this.touchableHandleLongPress(e);
        // }

        // QRN END

        if (newIsHighlight && !curIsHighlight) {
            this._startHighlight(e);
        } else if (!newIsHighlight && curIsHighlight) {
            this._endHighlight(e);
        }

        if (IsPressingIn[curState] && signal === Signals.RESPONDER_RELEASE) {
            const hasLongPressHandler = !!this.props.onLongPress;
            const pressIsLongButStillCallOnPress =
                IsLongPressingIn[curState] && // We *are* long pressing.. // But either has no long handler
                (!hasLongPressHandler || !this.touchableLongPressCancelsPress()); // or we're told to ignore it.

            const shouldInvokePress = !IsLongPressingIn[curState] || pressIsLongButStillCallOnPress;
            if (shouldInvokePress && this.touchableHandlePress) {
                if (!newIsHighlight && !curIsHighlight) {
                    // we never highlighted because of delay, but we should highlight now
                    this._startHighlight(e);
                    this._endHighlight(e);
                }
                if (Platform.OS === 'android' && !this.props.touchSoundDisabled) {
                    SoundManager.playTouchSound();
                }

                // QRN BEGIN 处理 press 的时候添加了QAV记录和获取的逻辑
                if (e.nativeEvent && e.nativeEvent.fromQAV) {
                    try {
                        this._getTouchEventXPath(e, 'press');
                    } catch (e) {}
                } else {
                    try {
                        this._recordTouchEvent(e, 'press');
                    } catch (e) {}
                    this.touchableHandlePress(e);
                }
                // del
                //this.touchableHandlePress(e);

                // QRN END
            }
        }

        this.touchableDelayTimeout && clearTimeout(this.touchableDelayTimeout);
        this.touchableDelayTimeout = null;
    },

    _startHighlight: function (e: PressEvent) {
        this._savePressInLocation(e);
        this.touchableHandleActivePressIn && this.touchableHandleActivePressIn(e);
    },

    _endHighlight: function (e: PressEvent) {
        if (this.touchableHandleActivePressOut) {
            if (this.touchableGetPressOutDelayMS && this.touchableGetPressOutDelayMS()) {
                this.pressOutDelayTimeout = setTimeout(() => {
                    this.touchableHandleActivePressOut(e);
                }, this.touchableGetPressOutDelayMS());
            } else {
                this.touchableHandleActivePressOut(e);
            }
        }
    },

    withoutDefaultFocusAndBlur: ({}: $TEMPORARY$object<{||}>),

    // QRN BEGIN 为了 QAV 额外添加的函数
    /**
     * hack 用户点击事件，发送点击模块d  xpath
     * @param {Event} e Native event.
     * @param {string} type longPress | press
     * @private
     */
    _recordTouchEvent: function (e: PressEvent, type: string) {
        var touchableNode = this.getTouchableNode(e._targetInst);
        var xpathInfo = this.getFiberXPath(touchableNode);
        if (QAV && QAV.send) {
            QAV.send(Object.assign(xpathInfo, { type: type }));
        }
    },

    /**
     * 在客户端中使用 qav 小蓝球，浮动到组件上方时，会触发模拟的点击事件
     * @param {Event} e Native event.
     * @param {string} type longPress | press
     * @private
     */
    _getTouchEventXPath: function (e, type) {
        var touchableNode = this.getTouchableNode(e._targetInst);
        var xpathInfo = this.getFiberXPath(touchableNode);
        if (QAV && QAV.getXPath) {
            QAV.getXPath(
                Object.assign({}, xpathInfo, {
                    pageX: e.nativeEvent.pageX,
                    pageY: e.nativeEvent.pageY,
                    type: type,
                    trackId,
                })
            );
        }
    },

    /**
     * @param node
     * @returns {null|*|null}
     */
    getTouchableNode: function (node) {
        if (node) {
            if (node.elementType.displayName && node.elementType.displayName.indexOf('touchable')) {
                return node;
            } else {
                return this.getTouchableNode(node.return);
            }
        } else {
            return null;
        }
    },

    /**
     * 通过 fiber 节点对象上的 index 属性生成组件 xpath
     * @param fiberNode
     * @returns {*}
     */
    getFiberXPath: function (fiberNode) {
        const patchArray = ['AppContainer', 'Modal', 'ScrollView'];
        const FOUND = 'FOUND_ROOT_TAG';
        const hybridId = HybridIdRegistry.hybridId || '';
        const customKey = (this.props['data-qav-ac-id'] || '') + '';
        const tag = fiberNode.tag;
        let current = fiberNode;
        let path = [];
        let rootViewTag = '__undefinedRootView'; // rootView Tag：用来区分不同的 rn 页面
        let xpath;
        let id;
        let viewName;
        /**
         * 可能出现 ScrollView 嵌套情况, 也就是多层 ScrollView
         */
        let plyCountOfSVArray = [];
        /**
         * plyCountOfModal 为针对 ScrollView 需要抹除的层数
         * [2019-11-15] 3: 没有过场动画，4: 有，多一层 AnimatedComponent
         * 可通过 QRNModal 内部的 animation 判断
         */
        let plyCountOfQRNModal = 3;

        const debugxxx = [];
        while (current) {
            /**
             * export const FunctionComponent = 0;
             * export const ClassComponent = 1;
             * export const IndeterminateComponent = 2; // Before we know whether it is function or class
             * export const HostRoot = 3; // Root of a host tree. Could be nested inside another node.
             * export const HostPortal = 4; // A subtree. Could be an entry point to a different renderer.
             * export const HostComponent = 5;
             * export const HostText = 6;
             * export const Fragment = 7;
             * export const Mode = 8;
             * export const ContextConsumer = 9;
             * export const ContextProvider = 10;
             * export const ForwardRef = 11;
             * export const Profiler = 12;
             * export const SuspenseComponent = 13;
             * export const MemoComponent = 14;
             * export const SimpleMemoComponent = 15;
             * export const LazyComponent = 16;
             */
            const recordTagList = [0, 1, 5, 11];
            let cIndex = current.index;

            if (current && recordTagList.includes(current.tag)) {
                const eleType = current.elementType;
                const eleTypeName = eleType && eleType.name;
                /**
                 * ROOT_TAG 的源码文件地址 react-native-ext/plugins/router/mix-redux/index.js
                 * QAV 的实现逻辑和之前有所出入, 按照之前的分段信息, 根节点到 ROOT_TAG 的上一层截止(为0)
                 * 之前的 QAV 的 xpath 按照 mix-redux/index.js 中的层级都是 viewName.0.x.x.x...
                 * 所以代码中找到 ROOT_TAG 后插入 FOUND 后续以 FOUND 分隔只保留后面的部分
                 * eg: 0.$1.0.0.0.1.FOUND_ROOT_TAG.0.1.0.0.1 => Demo.0.1.0.0.1
                 */
                const foundRootTag =
                    current.stateNode &&
                    current.stateNode.props &&
                    typeof current.stateNode.props[ROOT_TAG] === 'number';
                if (foundRootTag) {
                    path.unshift(FOUND, 0, cIndex);
                }

                // AppContainer 根节点, 找到后退出循环
                if (patchArray[0] === eleTypeName) {
                    const stateNode = current.stateNode;
                    rootViewTag = stateNode.props.rootTag;
                    viewName = AppRegistry.QAVRootTags[rootViewTag];
                    id = `${hybridId}@${viewName}`;
                    break;
                }

                /**
                 * 处理 Modal
                 * QRN Modal 有个 onMaskPress，从 Modal 开始 mask 的xpath 为 0.0.0
                 * 为了避免出现重复，Modal 的 children 从 1 开始
                 */
                if (patchArray[1] === eleTypeName) {
                    cIndex = cIndex + 'QRNModal';
                    const animation = current.stateNode.animation;
                    // QRN Modal, animation 为 null 时即为初始值(3), 否则(4)
                    if ('object' === typeof animation && animation) plyCountOfQRNModal++;
                    // Modal 的 child 为 RCTModalHostView 时为 RN 官方 Modal, 多两层
                    if ('RCTModalHostView' === current.child.type) plyCountOfQRNModal--;
                }

                /**
                 * Handle ScrollView START
                 *
                 * plyCountOfSV 为针对 ScrollView 需要抹除的层数
                 * qrn 的 ScrollView 组件外有一层 View ([2019-11-15] qrn:4, rn:3)
                 * 因自身需要计算为一层, 所以初始 plyCountOfSV: 3(4-1) 默认 qrn 的层级
                 */
                let plyCountOfSV = 3;
                if (patchArray[2] === eleTypeName) {
                    cIndex = cIndex + ':' + 'SV';
                }
                const svChild = ['AndroidHorizontalScrollView', 'RCTScrollView', 'RCTScrollableView'];
                if (svChild.includes(eleType)) {
                    if (current.return && current.return.elementType) {
                        const parentType = current.return.elementType;
                        if ('ScrollView' === parentType.name) {
                            // rn
                            plyCountOfSV--;
                        }
                        if ('AndroidSwipeRefreshLayout' === parentType) {
                            // rn android has refreshControl
                            plyCountOfSV++;
                        }
                    }
                    plyCountOfSVArray.unshift(plyCountOfSV);
                }
                /**
                 * Handle ScrollView END
                 */

                // Handle TouchableXXX START
                if (eleType && eleType.displayName) {
                    if ('TouchableOpacity' === eleType.displayName) cIndex = cIndex + '.TO';
                    if ('TouchableHighlight' === eleType.displayName) cIndex = cIndex + '.TH';
                }
                // Handle TouchableXXX END

                // Handle Button START
                const BtnChild = [
                    'TouchableNativeFeedback',
                    'TouchableWithoutFeedback',
                    'TouchableOpacity',
                    'TouchableCustomFeedback',
                    'TouchableHighlight',
                ];
                if (eleType && BtnChild.includes(eleType.displayName)) {
                    if (current.return && current.return.elementType && 'Button' === current.return.elementType.name) {
                        // Button 下一层不计数，跳过
                        current = current.return;
                        continue;
                    }
                }
                // Handle Button END

                // if (current.tag === recordTagList[1]) {
                //     cIndex = cIndex + '[ClassComponent]';
                // }

                const useKey = !patchArray.includes(eleTypeName);
                // 如果组件上有 key 属性, 拿来用
                if (current.key && useKey) cIndex = `$${current.key}`;

                path.unshift(cIndex);

                debugxxx.unshift({
                    id,
                    cIndex,
                    index: current.index,
                    tag: current.tag,
                    type: current.type,
                    propsKey: current.key,
                    current_elementType: eleType,
                    current_elementType_name: eleType && eleType.name,
                    current_elementType_displayName: eleType && eleType.displayName,
                });
            }

            current = current.return;
        }

        const sliceIdx = path.lastIndexOf(FOUND) + 1;
        if (sliceIdx > 0) path = [viewName, ...path.slice(sliceIdx)];

        xpath = path.join('.');

        /**
         * 在 FiberNode 中 TouchableOpacity 相关组件会多出 2 层
         */
        const TOReg = /\.TO(\.0){0,2}/g;
        if (TOReg.test(xpath)) xpath = xpath.replace(TOReg, '');

        /**
         * 在 FiberNode 中 TouchableHighlight 相关组件会多出 1 层
         */
        const THReg = /\.TH(\.0){0,1}/g;
        if (THReg.test(xpath)) xpath = xpath.replace(THReg, '');

        /**
         * Handle QRN Modal START
         * 处理 QRN Modal 多余的层级
         */
        const QRNModalReg = new RegExp(`(QRNModal(\\.\\d){${plyCountOfQRNModal}})\\.(\\d|\\$[^\\.]*)`);
        if (QRNModalReg.test(xpath)) {
            xpath = xpath.replace(QRNModalReg, (match, p1, p2, p3, offset, string) => {
                /**
                 * p3 为 Modal 的第一个 child, 为了避免 xpath 重复
                 * 在 p3 为数字(有可能出现带有 key 的情况: $xxx) 时自增1
                 */
                if (!isNaN(Number(p3))) p3++;
                return '.' + p3;
            });
        } else {
            xpath = xpath.replace('QRNModal', '');
        }
        /**
         * Handle QRN Modal END
         */

        /**
         * Handle ScrollView START
         * 处理 ScrollView 多余的层级
         */
        plyCountOfSVArray.map((count) => {
            const scrollViewReg = new RegExp(`SV(\\.[0-9]){0,${count}}\\.`);
            if (scrollViewReg.test(xpath)) xpath = xpath.replace(scrollViewReg, '');
        });
        xpath = xpath.replace(/SV\./g, '');
        /**
         * Handle ScrollView END
         */

        /**
         * The previous QAV START
         * 复制之前的 QAV 逻辑
         */
        const listReg = /(\$[rsk](_-?\d+(_-?\d+)?))|(\^ls[\.\:]?(\$?)([A-Za-z0-9_]+))/g;
        let position = '';
        let _matchedList = listReg.exec(xpath);
        let lastMatched = _matchedList;
        while (_matchedList) {
            lastMatched = _matchedList;
            _matchedList = listReg.exec(xpath);
        }
        xpath = xpath.replace(listReg, function (matched) {
            if (arguments[7] === lastMatched.index) {
                if (arguments[1]) {
                    // 匹配list
                    position = arguments[2].substr(1).replace('_', '-');
                    return matched.replace(arguments[2], '[-]');
                } else {
                    // 匹配模拟的list
                    var _keyArray = arguments[6].split('_');
                    var _location = _keyArray[_keyArray.length - 1];

                    position =
                        (arguments[5].length > 0 ? parseInt(_location, 10) : parseInt(_location, 36)) || _location;
                    position += '';
                    return '[-]';
                }
            } else {
                return matched;
            }
        });
        /**
         * The previous QAV END
         */

        // console.table(debugxxx.slice(5));
        const xpathInfo = {
            id,
            xpath,
            position,
            tag,
            rootViewTag,
            viewName,
            customKey,
        };
        return xpathInfo;
    },
    // QRN END
};

/**
 * Provide an optional version of t-he mixin where `touchableHandleFocus` and
 * `touchableHandleBlur` can be overridden. This allows appropriate defaults to
 * be set on TV platforms, without breaking existing implementations of
 * `Touchable`.
 */
const { touchableHandleFocus, touchableHandleBlur, ...TouchableMixinWithoutDefaultFocusAndBlur } = TouchableMixin;
TouchableMixin.withoutDefaultFocusAndBlur = TouchableMixinWithoutDefaultFocusAndBlur;
Touchable.Mixin = TouchableMixin;
