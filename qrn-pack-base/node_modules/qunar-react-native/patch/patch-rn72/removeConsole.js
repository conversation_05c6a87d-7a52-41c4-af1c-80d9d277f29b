/*
 * 移除线上和 beta 环境下所有的 console 内容
 * 由于 QRNPackager 打包时移除的console不全，不得不手手动重置为空
 * @date 2017-8-4
 * <AUTHOR>
 */
import {NativeModules} from 'react-native';
const QDeviceInfo = NativeModules.QRCTDeviceInfo;
const noop = () => {};
if (QDeviceInfo && QDeviceInfo.releaseType === 'release') {
    const _originConsole = global && global.console || console;
    const allConsolePropsArr = Object.keys(_originConsole);

   
    for(let num = 0 ; num < allConsolePropsArr.length; ){
        // allConsolePropsArr[num] == '_errorOriginal' ||
        if (allConsolePropsArr[num] == 'reportErrorsAsExceptions' ||
         allConsolePropsArr[num] == 'error'){
            allConsolePropsArr.splice(num,1);
        }else
        {
            num++;
        }
    }

    const newConsoleProps = allConsolePropsArr.reduce((acc, val) => {
        acc[val] = {
            configurable: true,
            enumerable: true,
            writable: true,
            value: noop
        };
        return acc;
    }, {});
    Object.defineProperties(_originConsole, newConsoleProps);
}
