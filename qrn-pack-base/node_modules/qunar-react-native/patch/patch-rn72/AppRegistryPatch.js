/**
 * 本文件是为了在AppRegistry的runApplication执行时，保存当前的HybridId，为了给QAV统计使用
 */

import { AppRegistry, NativeModules, Platform } from 'react-native';
import HybridIdRegistry from '../../extension/HybridIdRegistry/HybridIdRegistry.js';
const hybridIdKey = '__hybridId_do_not_change_or_you_will_be_fired';

const QAV = NativeModules.QAV;
const QNavigation = NativeModules.QNavigation;
const QRCTVCManager = NativeModules.QRCTVCManager;
const QDeviceInfo = NativeModules.QRCTDeviceInfo;
// rootTag -> appKey 的映射
AppRegistry.QAVRootTags = {};

// 当前项目是否使用 Ext
AppRegistry.__isUsingExt = false;
// 是否为首次启动
AppRegistry.__firstLaunch = true;

const _runApplication = AppRegistry.runApplication;
AppRegistry.runApplication = function (appKey: string, appParameters: any): void {
    let { rootTag, initialProps } = appParameters;
    // 设置 hybridId 的逻辑必须放到 runApplication 之前，否则第一个页面 QTouchableRegistry 注册时无法获取到
    // hybridId
    let hybridId;
    // 新版将旧版的 moduleName 和 qInitView 合并成一个 pageName
    let pageName = '',
        qInitView = '';
    let __isUsingExt = AppRegistry.__isUsingExt;
    // 默认打开当前 rn 项目的页面名字
    let __homePageName = '';
    // 所有异步加载 QView
    let __viewsAsyncRequire = {};
    // 所有已加载的 QView
    let __views = {};
    // runApplication 时实际传过去的名字
    let __appKey = appKey;
    const Ext = global.Ext;
    if (Ext && Ext.defaults && Ext.defaults.indexView) {
        __isUsingExt = AppRegistry.__isUsingExt = true;
        // 默认是 业务配置的 或者 require 进来的第一个 QView
        __homePageName = Ext.defaults.indexView;
        let Router = Ext.Router;
        __viewsAsyncRequire = Router._viewsAsyncRequire;
        __views = Router._views;
    }

    // rn63: LogBox 不参与 appKey 处理
    if (__appKey !== 'LogBox') {
        try {
            // 根据 initialProps 中的 hybridIdKey 获取 hybridId，同时设置到 HybridIdRegistry 中
            hybridId = initialProps[hybridIdKey];
            pageName = initialProps.pageName;
            qInitView = initialProps.qInitView;
            const moduleName = initialProps.moduleName;
            if ((!!moduleName && moduleName !== 'naive') || !!qInitView) {
                console.warn(
                    'scheme 中的 qInitView 和 moduleName 已经合并成 pageName，详情参考 http://ued.qunar.com/qrn/releases-3.0.0-JS.html#其它'
                );
            }

            if (typeof hybridId === 'string') {
                HybridIdRegistry.setHybridId(hybridId);
            } else {
                console.log('应用未注册 hybridId，可能导致 QAV 结果不正确。');
            }

            const originAppKey = __appKey;
            if (__isUsingExt) {
                if (!!pageName) {
                    __appKey = pageName;
                } else if (!!qInitView) {
                    __appKey = qInitView;
                } else {
                    __appKey = __homePageName;
                }
            }

            // QRN ADD 上报页面未注册
            if (!__views[__appKey]) {
                const requireModulesLogData = {
                    bizTag: 'APP',
                    operType: 'monitor',
                    bizType: 'app',
                    appcode: 'runApplication_monitor',
                    module: 'default',
                    page: 'AppRegistryPatch',
                    operTime: '*',
                    id: 'AppRunApplication',
                    ext: {
                        originKey: originAppKey,
                        appKey: __appKey,
                        viewsKyes: Object.keys(__views).join(','),
                    },
                };
                NativeModules.QAV && NativeModules.QAV.componentLog(requireModulesLogData);
            }
            // QRN END
        } catch (e) {}

        // 判断需要异步加载当前 _appKey 对应的 QView 时
        if (__isUsingExt && !__views[__appKey]) {
            const callback = __viewsAsyncRequire[__appKey];
            if (typeof callback === 'function') {
                callback();
            } else {
                console.error(`加载 ${__appKey} 页面出错，请确定注册了 ${__appKey} 的 QView 或异步注册了该页面。`);
            }
        }
        // RN68补充, 因Pressability和 AppRegistry 存在循环引用, 故保存到全局变量
        AppRegistry.QAVRootTags[rootTag] = __appKey;
        global.QAVRootTags = AppRegistry.QAVRootTags;

        // 只有在使用了 ext 且 pageName 不存在的时候告诉 native 首页的名字
        if (__isUsingExt && !pageName) {
            QNavigation.setHomeModuleName(__appKey);
        }
    }

    try {
        // QRN 仅第一次启动时 允许执行前置操作(阻塞)
        if (
            Ext &&
            Ext._beforeRunApplication &&
            typeof Ext._beforeRunApplication === 'function' &&
            AppRegistry.__firstLaunch
        ) {
            Ext._beforeRunApplication(() => {
                _runApplication(__appKey, appParameters);
                AppRegistry.__firstLaunch = false;
            });
        } else {
            _runApplication(__appKey, appParameters);
        }
    } catch (e) {
        // 线上环境删除关闭空白 VC
        if (QDeviceInfo && QDeviceInfo.releaseType === 'release') {
            if (Platform.OS === 'ios') {
                QRCTVCManager && QRCTVCManager.closeCurrentVC({});
            } else if (Platform.OS === 'android') {
                QNavigation && QNavigation.back({});
            }
        }

        e.message = e.message + '\n注意：此错误线上环境表现为跳回前一页面';
        throw e;
    }
};

const _unmountApplicationComponentAtRootTag = AppRegistry.unmountApplicationComponentAtRootTag;

AppRegistry.unmountApplicationComponentAtRootTag = function (rootTag: number) {
    /*
     * QAV 判断是否销毁当前 hybridId 项目，如果当前效果的 rootTag 和 下文中的 fromTag 相等，
     * 说明关闭当前了 QRN 项目，此时需要将 fromTag 置空
     * 这样才不会在下次重新进入当前项目是，QAV.pageToPage 发过的记录存在 fromTag 值
     */
    const fromTag = HybridIdRegistry.fromTag;
    if (fromTag === rootTag) {
        HybridIdRegistry.setFromTag(null);
    }
    if (AppRegistry.QAVRootTags[rootTag]) {
        delete AppRegistry.QAVRootTags[rootTag];
    }
    if (AppRegistry.__isUsingExt) {
        let viewName = Ext.Router._hasAllocatedRootTagMap[rootTag];
        let listenerViewKey = `${viewName}_${rootTag}`;
        delete Ext.Router._hasAllocatedRootTagMap[rootTag];
        delete Ext.Router._lifecycleListeners[listenerViewKey];
    }
    _unmountApplicationComponentAtRootTag(rootTag);
};

AppRegistry.unregisterComponent = function (appKey: string): string {
    // 通过调用 registerRunnable，注册一个空函数达到 unregister 的目的
    AppRegistry.registerRunnable(appKey, null);
    return appKey;
};
