import { NativeModules, DeviceEventEmitter, AppRegistry } from 'react-native';
import HybridIdRegistry from '../../extension/HybridIdRegistry/HybridIdRegistry.js';
const QAV = NativeModules.QAV;

/**
 * QAV.pageToPage 页面跳转记录
 */
DeviceEventEmitter.addListener('onShow', opts => {
    const { name, index = null } = opts;
    const hybridId = HybridIdRegistry.hybridId;
    const to = `${hybridId}@${name}`;
    const fromTag = HybridIdRegistry.fromTag;
    const from = `${hybridId}@${AppRegistry.QAVRootTags[fromTag]}` || '';
    if (QAV && QAV.pageToPage) {
        try {
            QAV.pageToPage({ from, to, fromTag, toTag: index });
        } catch (e) {}
    }
    HybridIdRegistry.setFromTag(index);
});
