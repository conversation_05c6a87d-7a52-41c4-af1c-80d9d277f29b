/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow
 */

import type { IPerformanceLogger } from '../Utilities/createPerformanceLogger';
// QRN ADD 合并RN issue 44665
import type { ViewStyleProp } from '../StyleSheet/StyleSheet';
// QRN END

import GlobalPerformanceLogger from '../Utilities/GlobalPerformanceLogger';
import PerformanceLoggerContext from '../Utilities/PerformanceLoggerContext';
import AppContainer from './AppContainer';
import DisplayMode, { type DisplayModeType } from './DisplayMode';
import getCachedComponentWithDebugName from './getCachedComponentWithDebugName';
import * as Renderer from './RendererProxy';
import invariant from 'invariant';
import * as React from 'react';

// require BackHandler so it sets the default handler that exits the app if no listeners respond
import '../Utilities/BackHandler';
// QRN ADD
const NativeModules = require('../BatchedBridge/NativeModules');
// QRN END

type OffscreenType = React.AbstractComponent<{
    mode: 'visible' | 'hidden',
    children: React.Node,
}>;

export default function renderApplication<Props: Object>(
    RootComponent: React.ComponentType<Props>,
    initialProps: Props,
    rootTag: any,
    WrapperComponent?: ?React.ComponentType<any>,
    // QRN ADD 合并RN issue 44665
    rootViewStyle?: ?ViewStyleProp,
    // QRN END
    fabric?: boolean,
    showArchitectureIndicator?: boolean,
    scopedPerformanceLogger?: IPerformanceLogger,
    isLogBox?: boolean,
    debugName?: string,
    displayMode?: ?DisplayModeType,
    useConcurrentRoot?: boolean,
    useOffscreen?: boolean,
) {
    invariant(rootTag, 'Expect to have a valid rootTag, instead got ', rootTag);

    const performanceLogger = scopedPerformanceLogger ?? GlobalPerformanceLogger;

    // QRN ADD 检测 Render 是否执行完成, 如超 3 秒, 认为白屏
    let timeout = null,
        qWhiteScreenLayoutCB = null,
        qClearTimeoutCB = null;

    if (!isLogBox) {
        timeout = setTimeout(() => {
            if (global.QrnConfig && global.QrnConfig['whiteScreenErrorReportEnable'] === false) {
                return;
            }

            reportWhiteScreen(debugName);

        }, 3000);

        qWhiteScreenLayoutCB = (e) => {
            if (!e || !e.nativeEvent) {
                return;
            }

            const nativeEvent = e.nativeEvent;
            const { layout = {} } = nativeEvent;
            const { width = 0, height = 0 } = layout;


            if (width && height) {
                timeout && clearTimeout(timeout);
                timeout = null;
            }
        }

        qClearTimeoutCB = () => {
            timeout && clearTimeout(timeout);
            timeout = null;
        }
    }
    // QRN END

    let renderable: React.MixedElement = (
        <PerformanceLoggerContext.Provider value={performanceLogger}>
            <AppContainer
                rootTag={rootTag}
                fabric={fabric}
                // QRN ADD
                qWhiteScreenLayoutCB={qWhiteScreenLayoutCB}
                qClearTimeoutCB={qClearTimeoutCB}
                // QRN END
                showArchitectureIndicator={showArchitectureIndicator}
                WrapperComponent={WrapperComponent}
                // QRN ADD 合并RN issue 44665
                rootViewStyle={rootViewStyle}
                // QRN END
                initialProps={initialProps ?? Object.freeze({})}
                internal_excludeLogBox={isLogBox}>
                <RootComponent {...initialProps} rootTag={rootTag} />
            </AppContainer>
        </PerformanceLoggerContext.Provider>
    );

    if (__DEV__ && debugName) {
        const RootComponentWithMeaningfulName = getCachedComponentWithDebugName(
            `${debugName}(RootComponent)`,
        );
        renderable = (
            <RootComponentWithMeaningfulName>
                {renderable}
            </RootComponentWithMeaningfulName>
        );
    }

    // QRN ADD
    reportQrnPages({ debugName }, 'renderApplicationStart');
    // QRN END

    if (useOffscreen && displayMode != null) {
        // $FlowFixMe[incompatible-type]
        // $FlowFixMe[prop-missing]
        const Offscreen: OffscreenType = React.unstable_Offscreen;

        renderable = (
            <Offscreen
                mode={displayMode === DisplayMode.VISIBLE ? 'visible' : 'hidden'}>
                {renderable}
            </Offscreen>
        );
    }

    performanceLogger.startTimespan('renderApplication_React_render');
    performanceLogger.setExtra(
        'usedReactConcurrentRoot',
        useConcurrentRoot ? '1' : '0',
    );
    performanceLogger.setExtra('usedReactFabric', fabric ? '1' : '0');
    performanceLogger.setExtra(
        'usedReactProfiler',
        Renderer.isProfilingRenderer(),
    );
    Renderer.renderElement({
        element: renderable,
        rootTag,
        useFabric: Boolean(fabric),
        useConcurrentRoot: Boolean(useConcurrentRoot),
    });
    performanceLogger.stopTimespan('renderApplication_React_render');
    // QRN ADD
    reportQrnPages({ debugName }, 'renderApplicationEnd');
    // QRN END
}

// QRN ADD
// 白屏监控上报
const reportWhiteScreen = (pageName) => {
    const hybridid = global.__QP_INFO && global.__QP_INFO.hybridid;
    const qpVer = global.__QP_INFO && global.__QP_INFO.version;

    const data = {
        ext: {
            hybridId: hybridid,
            pageName
        },
        qpVer: String(qpVer),
        bizTag: 'APP',
        bizType: 'app',
        module: 'default',
        appcode: 'qrn-js',
        page: 'qrnPageWhiteScreenMonitor',
        id: 'whiteScreen',
        operType: 'monitor',
        key: 'app/qrnPageWhiteScreenMonitor/default/monitor/whiteScreen',
        operTime: String(new Date().getTime())
    };

    NativeModules.QAV && NativeModules.QAV.componentLog && NativeModules.QAV.componentLog(data);
};

const reportQrnPages = (ext, id) => {
    const logBaseData = {
        ext,
        bizType: 'app',
        bizTag: 'APP',
        module: 'default',
        appcode: 'qrn_js',
        page: 'qrn_pages',
        id,
        operType: 'enter',
        key: `app/qrn_pages/default/enter/${id}`,
        operTime: String(new Date().getTime())
    };

    NativeModules.QAV && NativeModules.QAV.componentLog && NativeModules.QAV.componentLog(logBaseData);

};
// QRN END
