'use strict';

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { StyleSheet, View, Text, ViewPropTypes } from 'react-native';
import Loading from '../Loading/Loading';
import TouchableCustomFeedback from '../TouchableCustomFeedback/TouchableCustomFeedback.js';
import ColorConfig from '../../ColorConfig.js';

const defaultProps = {
    disabled: false,
    loading: false,
    loadingContent: '\uf089',
    numberOfLines: 0,
    text: 'Button',
    style: {
        backgroundColor: ColorConfig['blue-light'],
        borderColor: ColorConfig['blue-main'],
        borderWidth: 1,
        borderRadius: 5,
        padding: 5,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center',
        flexDirection: 'row',
    },
    activedStyle: {
        opacity: 0.4,
    },
    disabledStyle: {
        opacity: 0.4,
    },
    textStyle: {
        color: ColorConfig['blue-main'],
        fontSize: 14,
    },
    loadingStyle: {
        opacity: 0.4,
    },
};

const propTypes = {
    /**
     * 是否禁用
     *
     * @property disabled
     * @type bool
     * @default false
     * @description 如果设为true，则禁止此组件的一切交互。
     */
    disabled: PropTypes.bool,
    /**
     * 是否为 loading 状态
     *
     * @property loading
     * @type bool
     * @default false
     * @description 如果设为true，则此组件变为 loading 状态。
     */
    loading: PropTypes.bool,
    /**
     * 是否为 loading 状态
     *
     * @property loadingContent
     * @type string
     * @default '\uf089'
     * @description 如果设为true，则此组件变为 loading 状态。
     */
    loadingContent: PropTypes.string,
    /**
     * 最多显示行数
     *
     * @property numberOfLines
     * @version >= 2.0
     * @type number
     * @default 0
     * @description 按钮内的文字最多显示的行数，如果超过则裁剪文本。默认值为 0，表示没有限制。
     */
    numberOfLines: PropTypes.number,
    /**
     * 点击事件
     *
     * @property onPress
     * @type function
     * @description 点击之后调用此方法。
     */
    onPress: PropTypes.func,
    /**
     * 长按事件
     *
     * @property onLongPress
     * @type function
     * @description 长按之后调用此方法。
     */
    onLongPress: PropTypes.func,
    /**
     * 按入事件
     *
     * @property onPressIn
     * @type function
     * @description 按入之后调用此方法。
     */
    onPressIn: PropTypes.func,
    /**
     * 按出事件
     *
     * @property onPressOut
     * @type function
     * @description 按出之后调用此方法。
     */
    onPressOut: PropTypes.func,
    /**
     * 按钮文本
     *
     * @property text
     * @type string
     * @default 'Button'
     * @description 按钮上显示的文字
     */
    text: PropTypes.string,
    /**
     * 按钮激活文本
     *
     * @property activedText
     * @type string
     * @default 默认跟 text 属性值一样，除非单独指定
     * @description 按钮激活时上面显示的文字
     */
    activedText: PropTypes.string,
    /**
     * 按钮禁止文本
     *
     * @property disabledText
     * @type string
     * @default 默认跟 text 属性值一样，除非单独指定
     * @description 按钮禁用时上面显示的文字
     */
    disabledText: PropTypes.string,
    /**
     * 按钮样式
     *
     * @property style
     * @type ViewPropTypes
     * @default {
     *     backgroundColor: '#ffffff',
     *     borderColor: '#00bcd4',
     *     borderWidth: 1,
     *     borderRadius: 5,
     *     padding: 5,
     *     alignItems: 'center',
     *     justifyContent: 'center',
     *     alignSelf: 'center',
     * }
     * @description 按钮的样式
     *
     * **注意：此属性的如果传入样式，会以merge的形式覆盖默认样式。即：默认padding为5，如果padding改为10，并不会影响其他的默认样式**
     */
    style: ViewPropTypes.style,
    /**
     * 按钮激活样式
     *
     * @property activedStyle
     * @type ViewPropTypes
     * @default 此属性默认在 style 属性的基础上，加上了{opacity: 0.4}
     * @description 按钮激活时的样式
     *
     * **注意：此属性的如果传入样式，会以merge的形式覆盖默认样式。即：默认padding为5，如果padding改为10，并不会影响其他的默认样式**
     */
    activedStyle: ViewPropTypes.style,
    /**
     * 按钮禁止样式
     *
     * @property disabledStyle
     * @type ViewPropTypes
     * @default 此属性默认在 style 属性的基础上，加上了{opacity: 0.4}
     * @description 按钮禁止时的样式
     *
     * **注意：此属性的如果传入样式，会以merge的形式覆盖默认样式。即：默认padding为5，如果padding改为10，并不会影响其他的默认样式**
     */
    disabledStyle: ViewPropTypes.style,
    /**
     * 按钮 loading 样式
     *
     * @property loadingStyle
     * @type ViewPropTypes
     * @default 此属性默认在 style 属性的基础上，加上了{opacity: 0.4}
     * @description 按钮 loading 时的样式
     *
     * **注意：此属性的如果传入样式，会以merge的形式覆盖默认样式。即：默认fontSize为14，如果fontSize改为18，并不会影响其他的默认样式**
     */
    loadingStyle: ViewPropTypes.style,
    /**
     * 文本样式
     *
     * @property textStyle
     * @type ViewPropTypes
     * @default {
     *    color: '#00bcd4',
     *    fontSize: 14,
     * }
     * @description 文本的样式
     *
     * **注意：此属性的如果传入样式，会以merge的形式覆盖默认样式。即：默认fontSize为14，如果fontSize改为18，并不会影响其他的默认样式**
     */
    textStyle: Text.propTypes.style,
    /**
     * 文本激活样式
     *
     * @property activedTextStyle
     * @type ViewPropTypes
     * @default 此属性默认跟 textStyle 一样
     * @description 文本激活时的样式
     *
     * **注意：此属性的如果传入样式，会以merge的形式覆盖默认样式。即：默认fontSize为14，如果fontSize改为18，并不会影响其他的默认样式**
     */
    activedTextStyle: Text.propTypes.style,
    /**
     * 文本禁止样式
     *
     * @property disabledTextStyle
     * @type ViewPropTypes
     * @default 此属性默认跟 textStyle 一样
     * @description 文本禁止时的样式
     *
     * **注意：此属性的如果传入样式，会以merge的形式覆盖默认样式。即：默认fontSize为14，如果fontSize改为18，并不会影响其他的默认样式**
     */
    disabledTextStyle: Text.propTypes.style,
    /**
     * 文本 loading 样式
     *
     * @property loadingTextStyle
     * @type ViewPropTypes
     * @default 此属性默认跟 textStyle 一样
     * @description 文本 loading 时的样式
     *
     * **注意：此属性的如果传入样式，会以merge的形式覆盖默认样式。即：默认fontSize为14，如果fontSize改为18，并不会影响其他的默认样式**
     */
    loadingTextStyle: Text.propTypes.style,
};

/**
 * 按钮组件
 *
 * @component Button
 * @example ./Playground/js/Examples/ButtonExample.js[1-90]
 * @version >=v1.0.0
 * @description 渲染出一个按钮。
 *
 * ![Loading](./images/component-Button.gif)
 */
class Button extends Component {
    _onPress() {
        const { onPress } = this.props;
        onPress && onPress.apply(this);
    }

    render() {
        const { disabled, loading, loadingContent: propsLoadingContent, text } = this.props;

        let buttonDisabled = disabled || loading;

        let style = [defaultProps.style, this.props.style],
            textStyle = [defaultProps.textStyle, this.props.textStyle];

        let activedStyle = [style, this.props.activedStyle],
            disabledStyle = [style, defaultProps.disabledStyle, this.props.disabledStyle],
            loadingStyle = [style, defaultProps.loadingStyle, this.props.loadingStyle],
            activedTextStyle = [textStyle, this.props.activedTextStyle],
            disabledTextStyle = [textStyle, this.props.disabledTextStyle],
            loadingTextStyle = StyleSheet.flatten([textStyle, this.props.loadingTextStyle]),
            activedText = this.props.activedText || text,
            disabledText = this.props.disabledText || text,
            loadingText = this.props.loadingText || text;

        const fontSize = loadingTextStyle.fontSize;
        const color = loadingTextStyle.color;

        let loadingContent = (
            <View style={loadingStyle}>
                <View style={{ flex: 0 }}>
                    <Loading color={color} size={fontSize} content={propsLoadingContent} />
                </View>
                <View style={{ flexGrow: 0, flexShrink: 1 }}>
                    <Text numberOfLines={this.props.numberOfLines} style={loadingTextStyle}>
                        {loadingText}
                    </Text>
                </View>
            </View>
        );

        return (
            <TouchableCustomFeedback
                disabled={buttonDisabled}
                onPress={() => {
                    this.props.onPress && this.props.onPress.apply(this);
                }}
                onLongPress={() => {
                    this.props.onLongPress && this.props.onLongPress.apply(this);
                }}
                onPressIn={() => {
                    this.props.onPressIn && this.props.onPressIn.apply(this);
                }}
                onPressOut={() => {
                    this.props.onPressOut && this.props.onPressOut.apply(this);
                }}
                defaultContent={
                    <View style={style}>
                        <Text numberOfLines={this.props.numberOfLines} style={textStyle}>
                            {text}
                        </Text>
                    </View>
                }
                activedContent={
                    <View style={activedStyle}>
                        <Text numberOfLines={this.props.numberOfLines} style={activedTextStyle}>
                            {activedText}
                        </Text>
                    </View>
                }
                disabledContent={
                    loading ? (
                        loadingContent
                    ) : (
                        <View style={disabledStyle}>
                            <Text numberOfLines={this.props.numberOfLines} style={disabledTextStyle}>
                                {disabledText}
                            </Text>
                        </View>
                    )
                }
            />
        );
    }
}

Button.defaultProps = defaultProps;
Button.propTypes = propTypes;

module.exports = Button;
