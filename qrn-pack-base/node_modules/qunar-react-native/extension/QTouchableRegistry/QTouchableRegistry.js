/**
 * @providesModule QTouchableRegistry
 */

const BatchedBridge = require('react-native/Libraries/BatchedBridge/BatchedBridge');
const warning = require('fbjs/lib/warning');
const QAV = require('react-native').NativeModules.QAV;

var TouchableRegistry = {
    pathInfo: {},

    register: function (pathInfo) {
        const {id, xpath, position, tag, rootViewTag} = pathInfo;

        if (!this.pathInfo[rootViewTag]) {
            this.pathInfo[rootViewTag] = {};
        }

        if (!this.pathInfo[rootViewTag][tag]) {
            this.pathInfo[rootViewTag][tag] = {
                id,
                xpath,
                position,
                tag
            };
        }
    },

    unregister: function (pathInfo) {
        const {tag, rootViewTag} = pathInfo;

        if (this.pathInfo[rootViewTag] && this.pathInfo[rootViewTag][tag]) {
            delete this.pathInfo[rootViewTag][tag];
        }
    },

    getCurrentTouchable: function (rootViewTag) {
        var tags = [];

        if (this.pathInfo[rootViewTag]) {
            var _rootView = this.pathInfo[rootViewTag];

            for (var tag in _rootView) {
                tags.push(Object.assign({}, _rootView[tag]));
            }

            if (QAV && QAV.sendTouchableList) {
                QAV.sendTouchableList(tags);
            }
        } else {
            warning(false, `Cannot find the rootViewTag: ${rootViewTag}`);
            if (QAV && QAV.sendTouchableList) {
                QAV.sendTouchableList(tags);
            }
        }
    }
};

BatchedBridge.registerCallableModule('TouchableRegistry', TouchableRegistry);

module.exports = TouchableRegistry;
