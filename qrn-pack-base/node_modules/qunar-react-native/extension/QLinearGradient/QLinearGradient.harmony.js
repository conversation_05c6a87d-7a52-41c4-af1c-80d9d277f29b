// @flow
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
    processColor,
    requireNativeComponent,
    PointPropType,
    StyleSheet,
    View,
    ViewPropTypes,
    Text,
} from 'react-native';

const convertPointWithFabric = (name, point) => {
    if (Array.isArray(point)) {
        console.warn(
            `LinearGradient '${name}' property shoule be an object with fields 'x' and 'y', ` +
                'Array type is deprecated.'
        );

        return {
            x: point[0],
            y: point[1],
        };
    }
    if (name === 'start' && point === undefined) {
        return {
            x: 0,
            y: 0,
        };
    }
    if (name === 'end' && point === undefined) {
        return {
            x: 0,
            y: 1,
        };
    }
    return point;
};

const convertPoint = (name, point) => {
    if (typeof global.__isFabric !== 'undefined') {
        return convertPointWithFabric(name, point);
    }

    return oldConvertPoint(name, point);
};

const oldConvertPoint = (name, point) => {
    if (Array.isArray(point)) {
        console.warn(
            `LinearGradient '${name}' property shoule be an object with fields 'x' and 'y', ` +
                'Array type is deprecated.'
        );
    }
    if (point !== null && typeof point === 'object') {
        return [point.x, point.y];
    }

    if (name === 'start' && point === undefined) {
        return [0, 0];
    }
    if (name === 'end' && point === undefined) {
        return [0, 1];
    }

    return point;
};

export class QLinearGradient extends Component {
    static propTypes = {
        start: PropTypes.oneOfType([PointPropType]),
        end: PropTypes.oneOfType([PointPropType]),
        colors: PropTypes.arrayOf(PropTypes.string).isRequired,
        locations: PropTypes.arrayOf(PropTypes.number),
        ...ViewPropTypes,
    };
    props: PropsType;
    gradientRef: any;

    setNativeProps(props: PropsType) {
        this.gradientRef.setNativeProps(props);
    }

    render() {
        const { children, colors, end, locations, start, style, ...otherProps } = this.props;

        if (colors && locations && colors.length !== locations.length) {
            console.warn('LinearGradient colors and locations props should be arrays of the same length');
            return null;
        }

        // colors 至少要设置两种颜色
        if (!Array.isArray(colors) || colors.length === 0 || colors.length === 1) {
            console.warn('LinearGradient colors length must be array more than two object');
            return null;
        }

        // start=end的时候打警告处理
        if (start && end && start.x === end.x && start.y === end.y) {
            console.warn('LinearGradient start should not equal to end');
            return null;
        }

        //locations的范围是 0~1
        var isLocationsCorrect = true;
        if (Array.isArray(locations)) {
            locations.forEach((v) => {
                if (v < 0 || v > 1) {
                    isLocationsCorrect = false;
                }
            });
        }
        if (!isLocationsCorrect) {
            console.warn('LinearGradient locations object must be 0~1');
            return null;
        }

        // inherit container borderRadius until this issue is resolved:
        // https://github.com/facebook/react-native/issues/3198
        const flatStyle = StyleSheet.flatten(style) || {};
        const borderRadius = flatStyle.borderRadius || 0;

        // this is the format taken by:
        // http://developer.android.com/reference/android/graphics/Path.html#addRoundRect(android.graphics.RectF, float[], android.graphics.Path.Direction)
        const borderRadiiPerCorner = [
            flatStyle.borderTopLeftRadius || borderRadius,
            flatStyle.borderTopLeftRadius || borderRadius,
            flatStyle.borderTopRightRadius || borderRadius,
            flatStyle.borderTopRightRadius || borderRadius,
            flatStyle.borderBottomRightRadius || borderRadius,
            flatStyle.borderBottomRightRadius || borderRadius,
            flatStyle.borderBottomLeftRadius || borderRadius,
            flatStyle.borderBottomLeftRadius || borderRadius,
        ];

        return (
            <View
                ref={(component) => {
                    this.gradientRef = component;
                }}
                {...otherProps}
                style={[{
                    overflow: 'hidden'
                }, style]}>
                <QLinearGradientAndroid
                    style={{ position: 'absolute', top: 0, left: 0, bottom: 0, right: 0 }}
                    colors={colors.map(processColor)}
                    startPoint={convertPoint('start', start)}
                    endPoint={convertPoint('end', end)}
                    locations={locations ? locations.slice(0, colors.length) : null}
                    borderRadii={borderRadiiPerCorner}
                />
                {children}
            </View>
        );
    }
}

const QLinearGradientAndroid = requireNativeComponent('RNLinearGradient', null); //TODO 应该改成QLinearGradient

module.exports = QLinearGradient;
