/**
 *
 * @providesModule SendNotification
 */

'use strict';

import { NativeModules } from 'react-native';
const QRCTBroadCastManager = NativeModules.QRCTBroadCastManager;
import RCTDeviceEventEmitter from 'react-native/Libraries/EventEmitter/RCTDeviceEventEmitter.js';

var regex = /\w+-\w+-\w+/;

const SendNotification = {
    /**
     * 发送广播
     * @param name 广播的标识 必须是 部门-业务-功能 这种格式的，例如：flight-booking-detail
     * @param data 发送的广播数据
     **/
    sendNotification({ name, data, errorCallback }) {
        var OK = regex.test(name);
        if (!OK) {
            errorCallback && errorCallback({ error: '请输入正确的name' });
            return;
        }

        QRCTBroadCastManager.sendNotification(name, data);
    },

    /* 监听广播
     * @param name 广播的标识 必须是 部门-业务-功能 这种格式的，例如：flight-booking-detail
     * @param subscriptionCallback 回调出去的subscription，用于移除相应的subscription监听使用
     * @param dataCallback 数据回调
     * @param errorCallback 当name不符合格式时给出的错误提示
     **/
    addNotification({ name, subscriptionCallback, dataCallback, errorCallback }) {
        var OK = regex.test(name);
        if (!OK) {
            errorCallback && errorCallback({ error: '请输入正确的name' });
            return;
        }

        QRCTBroadCastManager.addNotification(name);
        var subscription = RCTDeviceEventEmitter.addListener(name, dataCallback);
        subscriptionCallback(subscription);
    },

    /* 移除监听广播
     * @param name 广播的标识 必须是 部门-业务-功能 这种格式的，例如：flight-booking-detail
     * @param subscription 从addNotification接口里拿到的subscription，用来正确移除这个监听
     * @param errorCallback 当name不符合格式时给出的错误提示
     **/
    removeNotification({ name, subscription, errorCallback }) {
        var OK = regex.test(name);
        if (!OK) {
            errorCallback && errorCallback({ error: '请输入正确的name' });
            return;
        }
        if (subscription) {
            subscription && subscription.remove();
        }

        //如果业务线的listener已经被移除完了 就移除native的监听
        if (RCTDeviceEventEmitter.listenerCount(name) == 0) {
            QRCTBroadCastManager.removeNotification(name);
        }
    },
};

if (!QRCTBroadCastManager) {
    console.warn('SendNotification native API 不存在');
    module.exports = null;
} else {
    module.exports = SendNotification;
}
