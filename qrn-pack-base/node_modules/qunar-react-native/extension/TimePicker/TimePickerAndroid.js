'use strict';

var React = require('react');
import PropTypes from 'prop-types';
var {
  StyleSheet,
  requireNativeComponent,
} = require('react-native');
var RCTTimePickerView = requireNativeComponent('RCTTimePickerView',TimePickerView);
const createReactClass = require('create-react-class');

var TimePickerView = createReactClass({
  name: 'TimePickerView',

  propTypes: {
    mode: PropTypes.oneOf([
      'datetime',
      'date',
      'month',
      'monthtime',
      'time',]),
    date:PropTypes.instanceOf(Date),
    minimumDate:PropTypes.instanceOf(Date),
    maximumDate:PropTypes.instanceOf(Date),
    minuteInterval: PropTypes.oneOf([0, 1, 2, 3, 5, 6, 10, 12, 15, 20, 30,]),
    timeZoneOffsetInMinutes: PropTypes.number,
    cyclic:PropTypes.bool,
    sound:PropTypes.bool,
    onChange: PropTypes.func,
  },

  getInitialState() {
    return initFromProps(this.props);
  },

  getDefaultProps() {
    return {
      mode: 'datetime',
      date: new Date(),
      minuteInterval: 1,
    };
  },

  _stateFromProps: function (props) {
    var dateArrayCur = verifyDateArray(props.date);
    return {dateArrayCur};
  },

  _onChange:function(event){
    var nativeEvent=event.nativeEvent;
    if (this.props.onChange) {
        var date = new Date();

        date.setFullYear(nativeEvent.year);
        date.setMonth(nativeEvent.month);
        date.setDate(nativeEvent.day);
        date.setHours(nativeEvent.hour);
        date.setMinutes(nativeEvent.min);

       this.props.onChange(date);
    }
  },

  render: function() {
    return (
        <RCTTimePickerView
            style={[styles.picker, this.props.style]}
            minuteInterval={this.props.minuteInterval}
            sound={this.props.sound}
            mode={this.props.mode}
            cyclic={this.props.cyclic}
            timeZoneOffsetInMinutes={this.state.timeZoneOffsetInMinutes}
            minimumDate={this.state.dateArrayMin}
            maximumDate={this.state.dateArrayMax}
            date={this.state.dateArrayCur} onChange={this._onChange}
        />
    )
  },
});

TimePickerView.getDerivedStateFromProps = function (nextProps) {
  return initFromProps(nextProps);
};

/**
 * 使用props生成state
 * @param props
 * @return {{timeZoneOffsetInMinutes: *, dateArrayMax: *, dateArrayCur: *, dateArrayMin: *}}
 */
function initFromProps(props) {
  var dateArrayCur = verifyDateArray(props.date);
  var dateArrayMin = verifyDateArray(props.minimumDate);
  var dateArrayMax = verifyDateArray(props.maximumDate);
  var timeZoneOffsetInMinutes = 240;

  return { dateArrayCur, dateArrayMin, dateArrayMax, timeZoneOffsetInMinutes };
}

/**
 * 校验日期，并返回年、月、日、时、分
 * @param date
 * @return {null|*[]}
 */
function verifyDateArray(date) {
  if (date === null || typeof date === 'undefined') {
    return null;
  }

  return [
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    date.getHours(),
    date.getMinutes()
  ];
}

var styles = StyleSheet.create({
     picker: {
       flexGrow:1,
       flexShrink: 1,
       height: 200,
       width:270,
     },
  });
module.exports = TimePickerView;
