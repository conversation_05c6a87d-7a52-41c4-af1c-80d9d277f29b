import { NativeModules } from "react-native";

const OriginDeviceInfo = NativeModules.QRCTDeviceInfo;
const DeviceInfo = NativeModules.QRCTDeviceInfo.getConstants(); // for 鸿蒙, 常量赋值只能用这种方式

const { getInfo: OriginGetInfo } = OriginDeviceInfo;
const getInfo = (fn) => {
  OriginGetInfo((DeviceInfoData) => {
    let hasGlobalInfo = getBundleInfo();
    if (hasGlobalInfo !== null) {
      DeviceInfoData.qpInfo = hasGlobalInfo;
    }
    fn(DeviceInfoData);
  });
};

const getBundleInfo = () => {
  if (typeof __QP_INFO !== "undefined") {
    __QP_INFO.version = "" + __QP_INFO.version;
    return __QP_INFO;
  } else {
    return null;
  }
};

DeviceInfo.getInfo = getInfo;
DeviceInfo.getAppInfoSync = () => {
  // 开启 debug in chrome 后 不能使用同步方法
  try {
    let hasGlobalInfo = getBundleInfo();

    return hasGlobalInfo === null
      ? OriginDeviceInfo.getAppInfoSync()
      : {
          qpInfo: hasGlobalInfo,
        };
  } catch (error) {
    throw new Error(
      "开启 debug in chrome 后 不能使用 getAppInfoSync 方法; 请使用 getInfo"
    );
  }
};

DeviceInfo.getInstalledAppInfo = (
  appSchemes = [],
  callback = () => {}
) => {
  // 参数验证
  if (!Array.isArray(appSchemes)) {
    console.warn(
      "[DeviceInfo.getInstalledAppInfo] appSchemes should be an array, converting..."
    );
    appSchemes = appSchemes ? [appSchemes] : [];
  }

  if (typeof callback !== "function") {
    console.warn(
      "[DeviceInfo.getInstalledAppInfo] callback should be a function, using empty function"
    );
    callback = () => {};
  }

  // 检查原生方法是否存在
  if (
    !OriginDeviceInfo ||
    typeof OriginDeviceInfo.getInstalledAppInfo !== "function"
  ) {
    console.error(
      "[DeviceInfo.getInstalledAppInfo] Native method not available"
    );
    callback({
      error: "Native getInstalledAppInfo method not available",
      installedApps: [],
    });
    return;
  }

  try {
    OriginDeviceInfo.getInstalledAppInfo(appSchemes, (result) => {
      try {
        // 确保结果格式正确
        if (!result || typeof result !== "object") {
          console.warn(
            "[DeviceInfo.getInstalledAppInfo] Invalid result from native, using empty array"
          );
          result = { installedApps: [] };
        }

        // 确保 installedApps 是数组
        if (!Array.isArray(result.installedApps)) {
          result.installedApps = [];
        }

        callback(result);
      } catch (callbackError) {
        console.error(
          "[DeviceInfo.getInstalledAppInfo] Callback processing error:",
          callbackError
        );
        callback({
          error: "Failed to process installed app info",
          details: callbackError.message,
          installedApps: [],
        });
      }
    });
  } catch (error) {
    console.error("[DeviceInfo.getInstalledAppInfo] Native call error:", error);
    callback({
      error: "Failed to call native getInstalledAppInfo method",
      details: error.message,
      installedApps: [],
    });
  }
};

module.exports = DeviceInfo;
