'use strict';

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { StyleSheet, View, Text, Animated, Easing, TouchableWithoutFeedback, ViewPropTypes } from 'react-native';
import ColorConfig from '../../ColorConfig.js';
const requireNativeComponent = require('react-native').requireNativeComponent;
const NOTICE = 'noticeContent';
const NOMORE = 'noMoreContent';
const LOADING = 'loadingContent';

/**
 * 上拉加载组件
 *
 * @component NativeLoadControl
 * @skip
 */
class LoadControl extends Component {

    constructor(props) {
        super(props);

        this.loading = false;
        this._nativeRef = null;
    }

    stopLoading(config = {}) {
        if (this.loading) {
            this.loading = false;
            this._nativeRef && this._nativeRef.setNativeProps && this._nativeRef.setNativeProps({loadComplete: true});
        }
    }

    _onLoad() {
        if (!this.loading) {
            this.loading = true;
            this.props && this.props.onLoad && this.props.onLoad();
        }
    }

    onPress() {
        if (!this.loading) {
            this.props.onPress && this.props.onPress();
        }
    }

    render() {
        return (
            <TouchableWithoutFeedback onPress={() => this.onPress()}>
                <AndroidLoadControl
                    {...this.props}
                    ref={ref => this._nativeRef = ref}
                    onLoad={()=>this._onLoad()}
                    style={[styles.contentContaienr, {height: this.props.height}]}
                    >
                    <Text style={[styles.icon, this.props.iconStyle]}>{""}</Text>
                    <Text style={[styles.content, this.props.textStyle]}>{this.props[NOTICE]}</Text>
                </AndroidLoadControl>
            </TouchableWithoutFeedback>

        );
    }
}

LoadControl.propTypes = {
    ...ViewPropTypes,
    /**
     * 高度
     *
     * @property height
    * @type number
     * @default 35
    * @description LoadControl的高度
     */
    height: PropTypes.number,
    /**
     * 没有更多
     *
     * @property noMore
    * @type bool
     * @default false
    * @description 如果为true，则显示 `NOMORE` 状态
     */
    noMore: PropTypes.bool,
    /**
     * 提示文本
     *
     * @property noticeContent
    * @type string
     * @default '上拉加载更多'
    * @description 显示在最下方的提示文本
     */
    noticeContent: PropTypes.string,
    /**
     * 加载文本
     *
     * @property loadingContent
    * @type string
     * @default '努力加载中'
    * @description 加载时的文本
     */
    loadingContent: PropTypes.string,
    /**
     * 加载图标
     *
     * @property loadingIcon
    * @type string
     * @default '\uf089'
    * @description 加载时旋转的图标
     * @version >=1.3.0
     */
    loadingIcon: PropTypes.string,
    /**
     * 没有更多文本
     *
     * @property noMoreContent
    * @type string
     * @default '没有更多了'
    * @description 没有更多时显示的文本
     */
    noMoreContent: PropTypes.string,
    /**
     * 刷新事件
     *
     * @property onLoad
     * @type function
    * @description
     *
     * 变成正在加载时触发的事件
     */
    onLoad: PropTypes.func,
    /**
     * 加载结果
     *
     * @property loadComplete
    * @type bool
     * @default false
    * @description
    *
    * 通知native加载结果，native停止刷新
     */
    loadComplete: PropTypes.bool,
    /**
     * 点击事件
     *
     * @property onPress
     * @type function
    * @description
     *
     * 点击LoadControl时触发的事件
     */
    onPress: PropTypes.func,
    /**
     * 样式
     *
     * @property style
    * @type ViewPropTypes
    * @description LoadControl样式
     */
    style: ViewPropTypes.style,
    /**
     * 文本样式
     *
     * @property textStyle
     * @type Text.propTypes.style
     * @description LoadControl的文本样式
     * @version >=1.3.0
     */
    textStyle: Text.propTypes.style,
    /**
     * 按钮样式
     *
     * @property iconStyle
     * @type Text.propTypes.style
     * @description LoadControl的按钮样式
     * @version >=1.3.0
     */
    iconStyle: Text.propTypes.style,
};

LoadControl.defaultProps = {
    height: 35,
    noMore: false,
    noticeContent: '上拉加载更多',
    loadingContent: '努力加载中',
    loadingIcon: '\uf089',
    noMoreContent: '没有更多了'
};


const styles = StyleSheet.create({
    container: {
     flexGrow: 1,
     flexShrink: 1,
     alignItems: 'center',
     justifyContent: 'center',
    },
    contentContaienr: {
     flexGrow: 1,
     flexShrink: 1,
     alignItems: 'center',
     justifyContent: 'center',
     flexDirection: 'row',
    },
    content: {
        backgroundColor: "transparent",
        color: ColorConfig['blue-main'],
        fontSize: 14,
    },
    icon: {
        backgroundColor: "transparent",
        color: ColorConfig['blue-main'],
        fontSize: 16,
        fontFamily: 'qunar_react_native',
        width: 16,
        height: 16,
        marginRight: 3,
    }
});

const AndroidLoadControl = requireNativeComponent('AndroidListLoadControl', LoadControl);

module.exports = LoadControl;
