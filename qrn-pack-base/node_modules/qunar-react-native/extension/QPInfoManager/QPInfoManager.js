/**
 *
 * @providesModule qpInfoManager
 */

'use strict';

const QRCTQpInfoManager = require('react-native').NativeModules.QRCTQpInfoManager;
const callBackProxy = (fn = () => { }, hybridId) => ({ version, ...rest } = {}) => {
    let hasGlobalInfo = typeof __QP_INFO !== 'undefined';
    if (hasGlobalInfo)
    {
        const { version: qpVersion, hybridid: qpHybridId = '' } = __QP_INFO || {};
        if (qpHybridId === hybridId)
        {
            fn({ ...rest, version: '' + qpVersion });
            return;
        }
    }

    fn({ ...rest, version });
    return;
};
const qpInfoManager = {

    /**
     * 获取qp包信息
     * 根据hybridId获取
     **/
    getOriginQpInfo(hybridId, callBack, failCallBack) {
        QRCTQpInfoManager.getOriginQpInfo(hybridId, callBackProxy(callBack, hybridId), failCallBack);
    },


    /**
    * 获取qp包信息
    * 会自动根据当前平台尝试添加_ios或者_android来获取qp包
    * 如果获取不到，再使用没添加后缀的hybird来获取
    **/
    getQpInfo(hybridId, callBack, failCallBack) {
        QRCTQpInfoManager.getQpInfo(hybridId, callBackProxy(callBack, hybridId), failCallBack);
    }
    ,
    /**
    * 查询hybridid的可用版本（强更/下线状态下返回最新可用版本），不依赖native层的hybridInfo
    * @param {*} hybridId
    * @param {*} callBack
    * @param {*} failCallBack
    */
    getQpPotentialLatestVersion(hybridId, callBack, failCallBack) {
        if (QRCTQpInfoManager && QRCTQpInfoManager.getQpPotentialLatestVersion)
        {
            QRCTQpInfoManager.getQpPotentialLatestVersion(hybridId, callBack, failCallBack);
        } else
        {
            failCallBack && failCallBack({ error: '请检查版本' });
        }
    },
    /**
    * 根据场景id和hybridid查询qp资源中信息
    * @param {*} sceneId
    * @param {*} callBack
    * @param {*} failCallBack
    */
    getQpModelInfoWithScene(sceneId, callBack, failCallBack) {
        if (QRCTQpInfoManager && QRCTQpInfoManager.getQpModelInfoWithScene)
        {
            QRCTQpInfoManager.getQpModelInfoWithScene(sceneId, callBack, failCallBack);
        } else
        {
            failCallBack && failCallBack({ error: '请检查版本' });
        }
    },

    /**
    * 根据 qp 包中的资源 URL 和 hybridId 获取对应资源内容。
    *
    * - 如果资源是文本类型（如 JSON、JS 函数等），将以字符串形式返回。
    * - 如果资源是非文本类型（如 PNG、TTF 或其他二进制数据），将以 base64 字符串返回。
    *
    * @param {string} url - 资源的 URL。
    * @param {string} hybridId - 对应的 hybridId。
    * @param {boolean} isStringType - 指定资源是否为字符串类型。
    * @param {function} callBack - 成功回调，返回 string（文本或 base64）。
    * @param {function} failCallBack - 失败回调，返回 error 信息。
    */
    getQpDataWithURL(url, hybridId, isStringType, callBack, failCallBack) {
        if (QRCTQpInfoManager && QRCTQpInfoManager.getQpDataWithURL)
        {
            QRCTQpInfoManager.getQpDataWithURL(url, hybridId, isStringType, callBack, failCallBack);
        } else
        {
            failCallBack && failCallBack({ error: '请检查版本' });
        }
    },
};
module.exports = qpInfoManager;
