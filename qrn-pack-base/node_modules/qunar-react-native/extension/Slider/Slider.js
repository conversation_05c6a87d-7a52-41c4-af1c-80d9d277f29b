/**
 * @providesModule QSlider
 * @flow
 */
'use strict'

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {StyleSheet, View, PanResponder, PixelRatio, ViewPropTypes} from 'react-native';

/**
 * 滑动选择范围组件
 *
 * @component QSlider
 * @example ./Playground/js/Examples/SliderExample.js[1-65]
 * @version >=v1.0.0
 * @description 这不是一个受约束的组件。也就是说，如果你不更新值，在用户操作后，这个组件也不会还原到初始值。
 *
 * ![Slider](./images/component-Slider.png)
 */
class QSlider extends Component {
    constructor(props) {
        super(props)

        this.gestrueStart = 0 // 手势开始位置
        this.state = {
            calculatedPosition: 0, // 计算位置，有可能超出trackWidth
            tempCalculatedPosition: null, // 计算位置，有可能超出trackWidth
            thumbPosition: 0, // 滑块位置
            value: props.value,
            trackWidth: 0,
            visible: 0,
        };

        this.createPanResponder();
    }

    createPanResponder() {
        // get move start position
        this._panResponder = PanResponder.create({
            onStartShouldSetPanResponder: (evt, gestureState) => !this.props.disabled && true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => !this.props.disabled && true,
            onMoveShouldSetPanResponder: (evt, gestureState) => !this.props.disabled && true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => !this.props.disabled && true,
            onPanResponderTerminationRequest: () => false,

            // get move start position
            onPanResponderGrant: (evt, gestureState) => {
                this.gestrueStart = gestureState.x0
            },

            // get move offset and set new position
            onPanResponderMove: (evt, gestureState) => {
                const trackWidth = this.state.trackWidth,
                    setpWidth = this.props.step / (this.props.maximumValue - this.props.minimumValue) * this.state.trackWidth

                let moveOffset = gestureState.moveX - this.gestrueStart,
                    shouldUpdate = false

                // if step exists, calculate if thumb should move
                if (this.props.step > 0) {
                    if (Math.abs(moveOffset % setpWidth) > setpWidth / 2) {
                        moveOffset = Math.round(moveOffset / setpWidth) * setpWidth
                        this.gestrueStart += moveOffset
                    } else {
                        moveOffset = 0
                    }
                }

                // 这里使用calculatedPosition而不是thumbPosition，因为滑块计算位置可能超出轨道长度
                // 比如minimumValue=5，maximumValue=10，step=2时，从10向左滑，应该回到9，这时calculatedPosition为11，而thumbPosition为10
                let newPosition = this.state.calculatedPosition + moveOffset //FIXME should be this.state.calculatedPosition

                if (this.props.step > 0) {
                    newPosition = newPosition < 1e-8 ? 0 : newPosition
                    this.state.calculatedPosition = newPosition
                    shouldUpdate = true
                } else if (this.props.step === 0) {
                    this.state.tempCalculatedPosition = newPosition
                    shouldUpdate = true
                }

                // garantee in track range
                if (shouldUpdate) {
                    if (newPosition > trackWidth) {
                        newPosition = trackWidth
                    } else if (newPosition < 0) {
                        newPosition = 0
                    }

                    this.setState({
                        thumbPosition: newPosition,
                    })

                    this.convertOffsetToValue(newPosition)
                }
            },

            // decide which page to scroll to
            onPanResponderRelease: (evt, gestureState) => {
                const { onSlidingComplete } = this.props
                if (onSlidingComplete) {
                    onSlidingComplete(this.state.value)
                }

                this.state.calculatedPosition = this.state.tempCalculatedPosition
                    ? this.state.tempCalculatedPosition
                    : this.state.calculatedPosition
            },
        });
    }

    render() {
        const { thumbSize, maximumTrackTintColor, minimumTrackTintColor, style, trackTintStyle, thumbStyle } = this.props

        const containerStyle = {
            opacity: this.state.visible,
            height: thumbSize,
        }
        const trackStyle = {
            height: thumbSize,
        }
        const trackTintHeight = trackTintStyle && (trackTintStyle.height !== undefined || trackTintStyle.height !== null) ? trackTintStyle.height : 2
        const activeStyle = {
            ...trackTintStyle,
            top: (thumbSize - trackTintHeight) / 2,
            width: this.state.thumbPosition + thumbSize / 2,
            height: trackTintHeight,
            backgroundColor: minimumTrackTintColor,
        }
        const unactiveStyle = {
            ...trackTintStyle,
            height: trackTintHeight,
            backgroundColor: maximumTrackTintColor,
        }


        const _thumbStyle = {
            width: thumbSize,
            height: thumbSize,
            left: this.state.thumbPosition,
            borderRadius: thumbSize / 2,
        }

        return (
            <View style={[styles.container, containerStyle, style]}>
                <View style={[styles.track, trackStyle]} ref="track" onLayout={(event) => this.initLayout(event)}>
                    <View style={[styles.unactiveTrack, unactiveStyle]}></View>
                    <View style={[styles.activeTrack, activeStyle]}></View>
                    <View style={[styles.thumb, _thumbStyle, thumbStyle]}
                          {...this._panResponder.panHandlers}
                    >
                    </View>
                </View>
            </View>
        )
    }

    initLayout(e) {
        if (!this.state.visible) {
            let realValue = this.state.value
            if (this.state.value < this.props.minimumValue) {
                realValue = this.props.minimumValue
            }
            if (this.state.value > this.props.maximumValue) {
                realValue = this.props.maximumValue
            }
            const { width } = e.nativeEvent.layout,
                trackWidth = width - this.props.thumbSize,
                initPosition = (realValue - this.props.minimumValue) /
                    (this.props.maximumValue - this.props.minimumValue) * trackWidth

            this.setState({
                trackWidth: trackWidth,
                thumbPosition: initPosition,
                calculatedPosition: initPosition,
                visible: 1,
            })
        }
    }

    convertOffsetToValue(offset) {
        let value = this.props.minimumValue + (offset / this.state.trackWidth * (this.props.maximumValue - this.props.minimumValue))

        const { onValueChange, onSlidingComplete } = this.props

        // fix decimal
        value = isNaN(value) ? 0 : parseFloat(value.toPrecision(10))
        if (this.props.step !== 0 && this.props.step < 1) {
            const stepDecimalLen = this.props.step.toString().split('').length - 2
            value = Number(value.toFixed(stepDecimalLen))
        }

        if (value !== this.state.value && onValueChange) {
            onValueChange(value)
        }

        this.setState({
            value: value
        })
    }
}

QSlider.defaultProps = {
    thumbSize: 28,
    maximumTrackTintColor: '#b6b6b6',
    minimumTrackTintColor: '#0b6aff',
    maximumValue: 1,
    minimumValue: 0,
    step: 0,
    value: 0,
}

QSlider.propTypes = {
    /**
     * @property thumbStyle
     * @type ViewPropTypes,
     * @description 滑块的样式
     */
    thumbStyle: ViewPropTypes.style,

    /**
     * @property thumbSize
     * @type number
     * @description 滑块大小。
     */
    thumbSize: PropTypes.number,

    /**
     * @property trackTintStyle
     * @type number
     * @description 滑块轨道样式。
     *
     * 需要注意的是该样式的 backgroundColor 属性不会起作用。若要轨道的背景色，你需要定义 maximumTrackTintColor 和 minimumTrackTintColor 属性。
     */
    trackTintStyle: ViewPropTypes.style,

    /**
     * @property maximumTrackTintColor
     * @type string
     * @description 滑块右侧轨道的颜色。默认为蓝色。
     */
    maximumTrackTintColor: PropTypes.string,

    /**
     * @property minimumTrackTintColor
     * @type string
     * @description 滑块左侧轨道的颜色。默认为灰色。
     */
    minimumTrackTintColor: PropTypes.string,

    /**
     * @property maximumValue
     * @type number
     * @description 滑块的最大值（当滑块滑到最右端时表示的值）。默认为1。
     */
    maximumValue: PropTypes.number,

    /**
     * @property minimumValue
     * @type number
     * @description 滑块的最小值（当滑块滑到最左端时表示的值）。默认为0。
     */
    minimumValue: PropTypes.number,

    /**
     * @property step
     * @type number
     * @description 滑块的最小步长。这个值应该在0到(maximumValue - minimumValue)之间。默认值为0。
     */
    step: PropTypes.number,

    /**
     * @property value
     * @type number
     * @description 滑块的初始值。这个值应该在最小值和最大值之间。默认值是0。
     */
    value: PropTypes.number,

    /**
     * @property onValueChange
     * @type function
     * @param {number} value 当前的值
     * @description (value) => void
     *
     * 在用户拖动滑块的过程中不断调用此回调。
     */
    onValueChange: PropTypes.func,

    /**
     * @property onSlidingComplete
     * @type function
     * @param {number} value 当前的值
     * @description (value) => void
     *
     * 用户结束滑动的时候调用此回调。
     */
    onSlidingComplete: PropTypes.func,
    /**
     * @property style
     * @type ViewPropTypes,
     * @description 组件容器的样式
     */
     style: ViewPropTypes.style,
}

const styles = StyleSheet.create({
    container: {
        justifyContent: 'center',
    },
    track: {
        justifyContent: 'center',
    },
    unactiveTrack: {
        borderRadius: 2,
    },
    activeTrack: {
        position: 'absolute',
        left: 0,
        width: 100,
        borderRadius: 2,
    },
    thumb: {
        position: 'absolute',
        top: 0,
        left: 0,
        borderWidth: 1,
        borderColor: '#ddd',
        backgroundColor: '#fff',
    },
})

module.exports = QSlider;
