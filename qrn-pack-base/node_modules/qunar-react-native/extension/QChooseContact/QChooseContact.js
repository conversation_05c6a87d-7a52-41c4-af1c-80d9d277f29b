/**
 *
 * @providesModule QChooseContact
 */

'use strict'

import { NativeModules } from 'react-native';
const QRCTChooseContact = NativeModules.QRCTChooseContact;

const QChooseContact = {
    chooseContact(callback = () => {}) {
        QRCTChooseContact.chooseContact(callback);
    },
    chooseContactPhone(callback = () => {}) {
        QRCTChooseContact.chooseContactPhone(callback);
    },
    chooseContactWithoutPermission(callback = () => {}) {
        QRCTChooseContact.chooseContactWithoutPermission(callback);
    },
    chooseContactPhoneWithoutPermission(callback = () => {}) {
        QRCTChooseContact.chooseContactPhoneWithoutPermission(callback);
    },
};


module.exports = QChooseContact;
