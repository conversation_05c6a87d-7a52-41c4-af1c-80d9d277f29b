'use strict';

import React, {Component} from 'react';
import PropTypes from 'prop-types';

import {
	StyleSheet,
	View,
	Text,
	Easing,
	requireNativeComponent,
	UnimplementedView,
	Platform, ViewPropTypes
} from 'react-native';

import ColorConfig from '../../ColorConfig.js';

const PULLSTART = 'pullStartContent';
const PULLCONTINUE = 'pullContinueContent';
const REFRESHING = 'refreshingContent';
const SUCCESS = 'successContent';
const FAIL = 'failContent';

const defaultAnimated = true;
const defaultDuration = 300;

/**
 * 下拉刷新组件
 *
 * @component NativeRefreshControl
 * @skip
 */
class RefreshControl extends Component {
	constructor(props) {
		super(props);

		this.refreshing = false;
		this.state = {
			iconMessage: this.props.pullIcon,
			infoMessage: this.props.pullStartContent
		}
	}

	stopRefreshing(config = {}) {
		clearTimeout(this.stopRefreshingTimeId);

		config.animated = typeof config.animated === "boolean" ? config.animated : defaultAnimated;
		this._nativeRef && this._nativeRef.setNativeProps && this._nativeRef.setNativeProps({hasAnimated: config.animated});

		config.duration = typeof config.duration === "number" ? config.duration : defaultDuration;
		this._nativeRef && this._nativeRef.setNativeProps && this._nativeRef.setNativeProps({animateTime: config.duration});

		if (config.result === undefined) {
			this._nativeRef && this._nativeRef.setNativeProps && this._nativeRef.setNativeProps({refreshComplete: true});
			return;
		}

		this._nativeRef && this._nativeRef.setNativeProps && this._nativeRef.setNativeProps({refreshResult: config.result});

		const resultDuration = 300;
		this.stopRefreshingTimeId = setTimeout(() => {
			this._nativeRef && this._nativeRef.setNativeProps && this._nativeRef.setNativeProps({refreshComplete: true});
		}, resultDuration)
	}

	startRefreshing(config = {}) {
		this._nativeRef.setNativeProps({refreshing: true});
	}

	_onRefreshStateChange(info) {
		switch (info.nativeEvent.state) {
			case "stop":
				this.setState({iconMessage: this.props.pullIcon, infoMessage: this.props.pullStartContent});
				break;

			case "refreshing":
				this.setState({iconMessage: this.props.refreshingIcon, infoMessage: this.props.refreshingContent});
				this.props && this.props.onRefresh && this.props.onRefresh();
				break;

			case "trigger":
				this.setState({iconMessage: this.props.pullIcon, infoMessage: this.props.pullContinueContent});
				break;

			case "success":
				this.setState({iconMessage: this.props.successIcon, infoMessage: this.props.successContent});
				break;

			case "fail":
				console.log('this.props.failIcon', this.props.failIcon);
				this.setState({iconMessage: this.props.failIcon, infoMessage: this.props.failContent});
				break;
			default:
		}
	}

	render() {
		const {style: customStyle, iconStyle: customIconStyle, textStyle: customTextStyle} = this.props;
		return (
			<QRNRefreshControlView {...this.props} ref={ref => this._nativeRef = ref} style={[
				styles.container,
				customStyle, {
					height: this.props.height
				}
			]} onRefreshStateChange={this._onRefreshStateChange.bind(this)}>
				<Text style={[styles.icon, customIconStyle]}>{this.state.iconMessage}</Text>
				<Text style={[styles.content, customTextStyle]}>{this.state.infoMessage}</Text>
			</QRNRefreshControlView>
		);
	}
}

RefreshControl.propTypes = {
	...ViewPropTypes,
	/**
      * 高度
      *
      * @property height
     * @type number
      * @default 35
     * @description RefreshControl的高度，下拉距离超过该距离时松手，会开始刷新
      */
	height: PropTypes.number,
	/**
      * PULLSTART状态文本
      *
      * @property pullStartContent
     * @type string
      * @default '下拉可以刷新'
     * @description 正在下拉，未达到刷新高度时的文本
      */
	pullStartContent: PropTypes.string,
	/**
      * PULLCONTINUE状态文本
      *
      * @property pullContinueContent
     * @type string
      * @default '松开即可刷新'
     * @description 正在下拉，达到刷新高度时的文本
      */
	pullContinueContent: PropTypes.string,
	/**
      * PULL时的icon文本
      *
      * @property pullIcon
      * @version >=1.3.0
      * @type string
      * @default '\uf07b'
      * @description PULLSTART和PULLCONTINUE时旋转的icon
      */
	pullIcon: PropTypes.string,
	/**
      * REFRESHING状态文本
      *
      * @property refreshingContent
     * @type string
      * @default '努力加载中'
     * @description 正在刷新时的文本
      */
	refreshingContent: PropTypes.string,
	/**
      * REFRESHING时的icon文本
      *
      * @property refreshingIcon
      * @version >=1.3.0
      * @type string
      * @default '\uf089'
      * @description REFRESHING时的旋转的icon文本
      */
	refreshingIcon: PropTypes.string,
	/**
      * SUCCESS状态文本
      *
      * @property successContent
      * @version >= 1.3.0
     * @type string
      * @default '加载成功'
     * @description 加载成功时的文本
      */
	successContent: PropTypes.string,
	/**
      * SUCCESS状态icon文本
      *
      * @property successIcon
      * @version >=1.3.0
      * @type string
      * @default '\uf078'
      * @description 加载成功时的icon文本
      */
	successIcon: PropTypes.string,
	/**
      * FAIL状态文本
      *
      * @property failContent
      * @version >= 1.3.0
     * @type string
      * @default '加载失败'
     * @description 加载失败时的文本
      */
	failContent: PropTypes.string,
	/**
      * FAIL状态icon文本
      *
      * @property failIcon
      * @version >=1.3.0
      * @type string
      * @default '\uf077'
      * @description 加载失败时的icon文本
      */
	failIcon: PropTypes.string,

	refreshing: PropTypes.bool,
	/**
      * 刷新事件
      *
      * @property onRefresh
      * @type function
     * @description
      *
      * 切换到正在刷新状态时触发的事件
      */
	onRefresh: PropTypes.func,
	/**
      * 刷新结果
      *
      * @property onRefresh
      * @type bool
      * @default false
     * @description
      *
      *通知native加载结果，native停止刷新
      */
	refreshComplete: PropTypes.bool,
	/**
      * 样式
      *
      * @property style
     * @type ViewPropTypes
     * @description RefreshControl 容器样式
      */
	style: ViewPropTypes.style,
	/**
      * iconfont样式
      *
      * @property iconStyle
     * @type Text.propTypes.style
     * @description RefreshControl iconfont样式
      */
	iconStyle: Text.propTypes.style,
	/**
      * 提示文字样式
      *
      * @property textStyle
     * @type Text.propTypes.style
     * @description RefreshControl 提示文字样式
      */
	textStyle: Text.propTypes.style,
    refreshResult: PropTypes.bool,
    animateTime: PropTypes.string,
    hasAnimated: PropTypes.bool,
    onRefreshStateChange: PropTypes.func,
};

RefreshControl.defaultProps = {
	height: 35,
	pullStartContent: '下拉可以刷新',
	pullContinueContent: '松开即可刷新',
	pullIcon: '\uf07b',
	refreshingContent: '努力加载中',
	refreshingIcon: '\uf089',
	successContent: '加载成功',
	successIcon: '\uf078',
	failContent: '加载失败',
	failIcon: '\uf077'
};

const QRNRefreshControlView = requireNativeComponent('QRNRefreshControlView', RefreshControl);

const styles = StyleSheet.create({
	container: {
		position: 'absolute',
		left: 0,
		right: 0,
		alignItems: 'center',
		justifyContent: 'center',
		flexDirection: 'row',
		borderLeftWidth: 1,
		borderRightWidth: 1,
		borderColor: 'transparent'
	},
	content: {
		backgroundColor: "transparent",
		color: ColorConfig['blue-main'],
		fontSize: 14
	},
	icon: {
		backgroundColor: "transparent",
		color: ColorConfig['blue-main'],
		fontSize: 16,
		fontFamily: 'qunar_react_native',
		marginRight: 3
	}
});

module.exports = RefreshControl;
