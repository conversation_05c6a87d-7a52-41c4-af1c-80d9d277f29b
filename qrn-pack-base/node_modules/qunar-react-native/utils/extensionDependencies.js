// 预引用, 防止被 tree-shaking
export default () => {
    // QD 组件
    require('@qnpm/q-animatedTab');
    require('@qnpm/q-bottombar');
    require('@qnpm/q-components');
    require('@qnpm/qd-calendar');
    require('@qnpm/q-design-card');
    require('@qnpm/q-design-utils');
    require('@qnpm/q-datepicker');
    require('@qnpm/q-dialog');
    require('@qnpm/q-flatlist');
    require('@qnpm/q-floating-button');
    require('@qnpm/q-gesture-float');
    require('@qnpm/q-image-gallery');
    require('@qnpm/q-loading');
    require('@qnpm/q-location');
    require('@qnpm/q-lottie');
    require('@qnpm/q-navbar');
    require('@qnpm/q-numberInputer');
    require('@qnpm/q-popover');
    require('@qnpm/q-popup-layer');
    require('@qnpm/qportal');
    require('@qnpm/q-resize-image');
    require('@qnpm/q-review');
    require('@qnpm/q-scrollpicker');
    require('@qnpm/q-shadow');
    require('@qnpm/q-tab');
    require('@qnpm/q-tag');
    require('@qnpm/q-textInput');
    require('@qnpm/q-tips');
    require('@qnpm/q-theme');
    require('@qnpm/qrn-tools');

    // 火车票组件
    require('@qnpm/train-react-common');
    require('@qnpm/train_ta');

    // @qnpm
    require('@qnpm/react-native-ui-scrollable-tab-view');
    require('@qnpm/react-native-ui-swipe-listview');
    require('@qnpm/react-native-ui-popover');
    require('@qnpm/react-native-ui-carousel');

    // RN UI
    require('react-native-swiper');
    require('react-native-keyboard-aware-scroll-view');
    require('react-native-snap-carousel');
    require('react-native-slider');
    require('react-native-communications');
    require('react-native-render-html');
    require('react-native-vector-icons');
    require('react-native-view-shot');
    require('react-native-linear-gradient');
    require('react-native-root-toast');
    require('react-native-calendars');
    require('react-native-video');
    require('react-native-picker');

    // 第三方
    require('mobx');
    require('mobx-react');
    require('moment');
    require('htmlparser2-without-node-native');
    require('react-router');
    require('entities');
    require('redux');
    require('react-redux');
    require('redux-thunk');
    require('lodash.get');
    require('lodash.isequal');
    require('lodash.once');
    require('qs');
    require('redux-batched-actions');
    require('xdate');
    require('recyclerlistview');
    require('reselect');
    require('querystring');
    require('crypto-js');
};
