import { Dimensions, Platform } from 'react-native';
import DeviceInfo from '../extension/DeviceInfo/DeviceInfo';

export function isIphoneX(width, height) {
    // let _width = width;
    // let _height = height;
    // _width && _height || (_width = Dimensions.get('window').width, _height = Dimensions.get('window').height);
    // return (
    //     Platform.OS === 'ios' &&
    //     !Platform.isPad &&
    //     !Platform.isTVOS &&
    //     (_width === 812 || _height === 812)
    // );
    return DeviceInfo.isIOS && DeviceInfo.isIphoneX;
}