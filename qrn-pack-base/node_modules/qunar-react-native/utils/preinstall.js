/**
 * This script is run before the package is installed.
 * 支持多次版本切换，使用动态引用机制
 */

const fs = require('fs');
const path = require('path');
const {getRNVersion, isVersionSupported} = require('./versionManager');

/**
 * 生成标准化的文件头注释
 * @param {string} fileName 文件名
 * @param {string} targetRN 目标RN版本
 * @returns {string} 标准化的注释
 */
function generateFileHeader(fileName, targetRN) {
    return `// ${fileName} 会根据 TARGET_RN 环境变量选择对应版本的文件
// 当前目标版本: ${targetRN}
// 此文件由 preinstall.js 自动生成，请勿手动修改`;
}

/**
 * 安全地写入文件，确保目录存在
 * @param {string} filePath 文件路径
 * @param {string} content 文件内容
 */
function safeWriteFile(filePath, content) {
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(filePath, content, 'utf8');
}

/**
 * 更新 patch/index.js 文件
 * @param {string} targetRN 目标RN版本
 * @param {string} targetRNLowerCase 小写的目标RN版本
 */
function updatePatchIndex(targetRN, targetRNLowerCase) {
    const targetPatchIndexPath = path.join(__dirname, '..', 'patch', 'index.js');
    const patchDirPath = path.join(__dirname, '..', 'patch', `patch-${targetRNLowerCase}`);

    // 检查目标patch目录是否存在
    if (!fs.existsSync(patchDirPath)) {
        console.warn(`[警告] patch目录不存在: patch-${targetRNLowerCase}`);
        return false;
    }

    const patchContent = `${generateFileHeader('patch/index.js', targetRN)}
import './patch-${targetRNLowerCase}/index.js';`;

    safeWriteFile(targetPatchIndexPath, patchContent);
    console.log(`[${targetRN}] 已更新 patch/index.js -> patch-${targetRNLowerCase}`);
    return true;
}

/**
 * 更新 utils/preRequire.js 文件，使用动态引用机制
 * @param {string} targetRN 目标RN版本
 * @param {string} targetRNLowerCase 小写的目标RN版本
 */
function updatePreRequireIndex(targetRN, targetRNLowerCase) {
    const targetPreRequirePath = path.join(__dirname, 'preRequire.js');
    const preRequireTemplatePath = path.join(__dirname, `preRequire-${targetRNLowerCase}.js`);

    // 检查目标preRequire文件是否存在
    if (!fs.existsSync(preRequireTemplatePath)) {
        console.warn(`[警告] preRequire模板文件不存在: preRequire-${targetRNLowerCase}.js`);
        return false;
    }

    const preRequireContent = `${generateFileHeader('utils/preRequire.js', targetRN)}
// 动态引用对应版本的 preRequire 实现
module.exports = require('./preRequire-${targetRNLowerCase}.js');`;

    safeWriteFile(targetPreRequirePath, preRequireContent);
    console.log(`[${targetRN}] 已更新 utils/preRequire.js -> preRequire-${targetRNLowerCase}.js`);
    return true;
}

// 主执行逻辑
const targetRN = getRNVersion();
console.log(`QRNJS 当前目标 RN 版本: ${targetRN}`);

// 验证版本是否支持
if (!isVersionSupported()) {
    console.error(`[错误] 不支持的RN版本: ${targetRN}`);
    console.error(`支持的版本: RN68, RN72, RN77`);
    process.exit(1);
}

const targetRNLowerCase = targetRN.toLowerCase();

// 总是更新配置文件，确保版本切换的一致性
let patchSuccess = true;
let preRequireSuccess = true;

// 更新 patch 配置
patchSuccess = updatePatchIndex(targetRN, targetRNLowerCase);

// 更新 preRequire 配置
preRequireSuccess = updatePreRequireIndex(targetRN, targetRNLowerCase);

// 输出结果
if (patchSuccess && preRequireSuccess) {
    console.log(`[${targetRN}] 版本切换完成`);
} else {
    console.error(`[${targetRN}] 版本切换过程中出现问题`);
    if (!patchSuccess) console.error('- patch 配置更新失败');
    if (!preRequireSuccess) console.error('- preRequire 配置更新失败');
}
