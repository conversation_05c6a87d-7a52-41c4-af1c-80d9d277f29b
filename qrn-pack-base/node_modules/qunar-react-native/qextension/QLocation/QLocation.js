`use strict`;

import { NativeModules, Platform } from 'react-native';
const LocationController = NativeModules.LocationController;
import QHotDogNetWork from '../QHotDogNetWork/QHotDogNetWork';
import HybridIdRegistry from '../../extension/HybridIdRegistry/HybridIdRegistry';
import DeviceInfo from '../../extension/DeviceInfo/DeviceInfo';

let uniqueId = 1,
    newUniqueId = 1;

const serviceType = 'p_location';
const IOS_NEW_API_VERSION = 80011288,
    ADR_NEW_API_VERSION = 60001522;
const ERR_MSG = { error: true, msg: '该api已被废弃，请使用新的api获取定位' };

const { vid, isIOS, isAndroid } = DeviceInfo;
const isSupportIosNewVersion = isIOS && vid >= IOS_NEW_API_VERSION;
const isSupportAdrNewVersion = isAndroid && vid >= ADR_NEW_API_VERSION;
const isNewVersion = isSupportAdrNewVersion || isSupportIosNewVersion || Platform.OS === 'harmony';

class QLocation {
    requestCacheLocation(callback = () => {}) {
        LocationController.cacheLocation(callback);
    }
    newRequestCurrentLocation(param, callback = () => {}) {
        let purpose = 'p_' + newUniqueId++ + '_' + (Math.random() + '').replace(/\D/g, '');
        if (isNewVersion) {
            this.newRequestCurrentLocationByPurpose(purpose, param, callback);
            return purpose;
        } else {
            return this.requestCurrentLocation(param, callback);
        }
    }
    newRequestCurrentLocationByPurpose(
        purpose,
        {
            timeout = -1,
            accuracy = LocationController.ACCURACY_IGNORE_LEVEL,
            authorizedAccuracy = LocationController.ACCURACY_REDUCED,
            locationTipKey = 'kDefaultSystemLocationTipKey',
            persistRequestPermission = false,
            forceLocation = false,
            accuracyAlert = false,
            enableHistoryLocStrategy = false,
        } = param,
        callback = () => {}
    ) {
        if (isNewVersion) {
            LocationController.newStartUpdateLocationWithPurposeV2(
                purpose,
                {
                    timeout,
                    accuracy,
                    authorizedAccuracy,
                    locationTipKey,
                    persistRequestPermission,
                    forceLocation,
                    accuracyAlert,
                    enableHistoryLocStrategy
                },
                result => {
                    callback(result);
                }
            );
        } else {
            this.requestCurrentLocationByPurpose(
                purpose,
                {
                    timeout,
                    accuracy,
                    authorizedAccuracy,
                    locationTipKey,
                    persistRequestPermission,
                    forceLocation,
                    accuracyAlert,
                },
                callback
            );
        }
    }
    // 以下为整改前的定位api
    getCacheLocation(callback = () => {}) {
        if (isNewVersion) {
            this.requestCacheLocation(callback);
        } else {
            LocationController.location(callback);
        }
    }

    requestCurrentLocation(param, callback = () => {}) {
        if (isNewVersion) {
            this.requestCacheLocation(callback); //对于旧的api 新版的包里只获取缓存
        } else {
            let purpose = 'p_' + uniqueId++ + '_' + (Math.random() + '').replace(/\D/g, '');
            this.requestCurrentLocationByPurpose(purpose, param, callback);
            return purpose;
        }
    }

    requestCurrentLocationByPurpose(
        purpose,
        {
            timeout = -1,
            accuracy = LocationController.ACCURACY_IGNORE_LEVEL,
            authorizedAccuracy = LocationController.ACCURACY_REDUCED,
            locationTipKey = 'kDefaultSystemLocationTipKey',
            persistRequestPermission = false,
            forceLocation = false,
            accuracyAlert = false,
        } = param,
        callback = () => {}
    ) {
        if (isNewVersion) {
            this.requestCacheLocation(callback); //对于旧的api 新版的包里只获取缓存
            return;
        }
        let timeoutFlag = false;
        let successFlag = false;

        const { vid, isIOS } = DeviceInfo;
        const isSupport = isIOS && +vid >= 80011240;

        const isSupportNativeTimeout = isIOS && +vid >= 80011270;
        const isSupportNativeTimeoutAdr = !isIOS && +vid >= 60001470;

        if (LocationController && LocationController.startUpdateLocationWithPurposeV2) {
            //new all
            LocationController.startUpdateLocationWithPurposeV2(
                purpose,
                {
                    timeout,
                    accuracy,
                    authorizedAccuracy,
                    locationTipKey,
                    persistRequestPermission,
                    forceLocation,
                    accuracyAlert,
                },
                result => {
                    if (!timeoutFlag) {
                        successFlag = true;
                        callback && callback(result);
                    }
                }
            );
        } else {
            //以下判断为了兼容旧版本
            if (isSupportNativeTimeout) {
                //ios timeout版本
                LocationController.startUpdateLocationWithPurpose(purpose, accuracy, authorizedAccuracy, locationTipKey, timeout, result => {
                    if (!timeoutFlag) {
                        successFlag = true;
                        callback && callback(result);
                    }
                });
            } else if (isSupport) {
                //ios 无timeout版本
                LocationController.startUpdateLocationWithPurpose(purpose, accuracy, authorizedAccuracy, locationTipKey, result => {
                    if (!timeoutFlag) {
                        successFlag = true;
                        callback && callback(result);
                    }
                });
            } else if (isSupportNativeTimeoutAdr) {
                //adr timeout版本

                LocationController.startUpdateLocationWithPurpose(purpose, accuracy, timeout, result => {
                    if (!timeoutFlag) {
                        successFlag = true;
                        callback && callback(result);
                    }
                });
            } else {
                //all 无timeout版本 无locationTipKey
                LocationController.startUpdatingLocationWithPurpose(purpose, accuracy, result => {
                    if (!timeoutFlag) {
                        successFlag = true;
                        callback && callback(result);
                    }
                });
            }
        }

        if (timeout > 0 && !(isSupportNativeTimeout || isSupportNativeTimeoutAdr)) {
            let self = this;
            setTimeout(() => {
                if (!successFlag) {
                    timeoutFlag = true;
                    self.stopRequestByPurpose(purpose);
                    let msg = 'Request timeout';
                    let error = true;
                    callback && callback({ msg, error });
                }
            }, timeout);
        }
    }

    async isLocationEnable() {
        if (Platform.OS === 'ios') {
            return true;
        }

        let enable;
        try {
            enable = new Promise(LocationController.isLocationEnableSync());
        } catch (e) {
            enable = await LocationController.isLocationEnableAsync();
        }

        return enable;
    }

    async openLocationSetting() {
        if (Platform.OS === 'ios') {
            throw new Error('该 API 不支持 ios 平台');
        }
        return await LocationController.openLocationSetting();
    }

    stopRequestByPurpose(purpose) {
        LocationController.stopUpdatingLocationWithPurpose(purpose);
    }

    requestCurrentCity(success, error) {
        this.requestCurrentCityV2({ timeout: 10 * 1000 }, success, error);
    }

    requestCurrentCityV2(param, success, error) {
        this.requestCurrentLocation(param, response => {
            if (response && !response.error) {
                this.requestCityInfoWithLocation(response, success, error);
            } else {
                error && error(response);
            }
        });
    }

    requestCityInfoWithLocation({ coordinate, coordinates, coordinateType } = location, success, error) {
        let coordConvert;
        let business = 2;
        let hybridId = HybridIdRegistry.hybridId;
        switch (coordinateType) {
            case 'BD09':
                coordConvert = 2;
                break;
            case 'WGS84':
                coordConvert = 0;
                break;
            case 'GCJ02':
                coordConvert = 1;
                break;
        }

        const theCoordinate = coordinate ? coordinate : coordinates ? coordinates : {};

        let param = { ...theCoordinate, coordConvert, business, type: 1, hybridId };

        QHotDogNetWork.postRequest({
            serviceType: serviceType,
            param: param,
            successCallback: response => {
                if (response && response.bstatus) {
                    let bstatus = response.bstatus;
                    let data = response.data;
                    if (bstatus.code != -1) {
                        success && success({ bstatus, data });
                    } else {
                        error && error({ bstatus });
                    }
                }
            },
            failCallback: data => {
                error && error(data);
            },
        });
    }
}

if (LocationController) {
    QLocation = new QLocation();
    QLocation.ACCURACY_IGNORE_LEVEL = LocationController.ACCURACY_IGNORE_LEVEL;
    QLocation.ACCURACY_HIGH_LEVEL = LocationController.ACCURACY_HIGH_LEVEL;
    QLocation.ACCURACY_NORMAL_LEVEL = LocationController.ACCURACY_NORMAL_LEVEL;
    QLocation.ACCURACY_LOW_LEVEL = LocationController.ACCURACY_LOW_LEVEL;

    QLocation.ACCURACY_REDUCED = LocationController.ACCURACY_REDUCED;
    QLocation.ACCURACY_FULL = LocationController.ACCURACY_FULL;
} else {
    QLocation = null;
}

module.exports = QLocation;
