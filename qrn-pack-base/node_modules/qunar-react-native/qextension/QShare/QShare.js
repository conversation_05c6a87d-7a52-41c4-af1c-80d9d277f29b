/**
 *
 * @providesModule QShare
 */

'use strict'

let QShareManager = require('react-native').NativeModules.QShareManager;

const _supportedShareTypes = [
  'wechatTimeline', 'wechatFriends', 'wechatFav',
  'QQZone', 'QQFriend', 'QQFav', 'sinaWeibo',
  'tencentWeibo', 'sms', 'mail', 'qunarFriend'
];

const QShare = {

  wechatTimeline: 'wechatTimeline', //朋友圈
  wechatFriends: 'wechatFriends', //微信好友
  wechatFav: 'wechatFav', //微信收藏
  QQZone: 'QQZone', //QQ空间
  QQFriend: 'QQFriend', //QQ好友
  QQFav: 'QQFav', //QQ收藏
  sinaWeibo: 'sinaWeibo', //新浪微博
  tencentWeibo: 'tencentWeibo', //腾讯微博
  sms: 'sms', //短信
  mail: 'mail', //邮件
  qunarFriend: 'qunarFriend', //去哪儿好友


  /**
   * 弹出弹层，选择某个平台分享
   * @param shareInfos 分享内容，包括图片，链接，名字，微信分享到小程序的小程序id和需要打开的页面path
   **/
  doShare(shareInfos: Object, callback = () => {}, failCallback = () => {}) {
    var shareInfoArray = [];
    var shareTypes = _supportedShareTypes;

    if (Array.isArray(shareInfos.types) && shareInfos.types.length) {
      shareInfos.types.map(type => {
        if (_supportedShareTypes.indexOf(type) < 0) {
          throw `shareType ${type} is not supported!`
        }
      })
      shareTypes = shareInfos.types;
    }
    shareTypes.map(type => {
      shareInfoArray = [...shareInfoArray, Object.assign({},shareInfos[type] ? shareInfos[type] : shareInfos.com, {
        type: type
      })];
    })
    QShareManager.doShare(shareInfoArray, shareInfos.com, callback, failCallback);
  },

  /**
   * 分享到微信，不弹出弹层，直接跳转到微信分享
   * @param
   **/
  wechatShare(shareInfos: Object, callback = () => {}, failCallback = () => {}) {
    QShareManager.wechatShare(shareInfos, callback, failCallback);
  }
};

if (!QShareManager) {
  console.warn('QShare API仅在大客户端环境内可用');
  module.exports = null;
} else {
  module.exports = QShare
}
