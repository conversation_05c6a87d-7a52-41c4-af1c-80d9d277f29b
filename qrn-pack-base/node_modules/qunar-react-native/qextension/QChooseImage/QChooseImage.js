/**
 *
 * @providesModule ChooseImage
 */

'use strict';

import { NativeModules } from 'react-native';
const QRCTChooseImage = NativeModules.QRCTChooseImage;

const ChooseImage = {
   chooseImage(param,successCallback,errorCallback) {
    QRCTChooseImage.chooseImage(param,successCallback,errorCallback);
   }
};

if (!QRCTChooseImage) {
    console.warn('QRCTChooseImage API仅在大客户端环境内可用');
    module.exports = null;
} else {
    module.exports = ChooseImage;
}
