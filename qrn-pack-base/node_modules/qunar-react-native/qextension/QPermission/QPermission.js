/**
 *
 * @providesModule QPermission
 */

'use strict';

import { NativeModules } from 'react-native';
const QRCTPermission = NativeModules.QRCTPermission;

const QPermission = {
    openPrefs(notificationInfos: Object, callback = () => {}, failCallback = () => {}) {
        QRCTPermission.openPrefs(notificationInfos, callback, failCallback);
    },
    checkPermission(notificationInfos: Object, callback = () => {}, failCallback = () => {}) {
        QRCTPermission.checkPermission(notificationInfos, callback, failCallback);
    },
    checkPermissions(permissions: Array, callback = () => {}, failCallback = () => {}) {
        NativeModules.QnrPermissionManager &&
            NativeModules.QnrPermissionManager.checkPhonePermission &&
            NativeModules.QnrPermissionManager.checkPhonePermission(permissions, callback, failCallback);
    },
    jumpAppSettings(callback = () => {}, failCallback = () => {}) {
        NativeModules.QnrPermissionManager &&
            NativeModules.QnrPermissionManager.jumpAppSettings &&
            NativeModules.QnrPermissionManager.jumpAppSettings(callback, failCallback);
    }
};

module.exports = QPermission;
