'use strict';

const QRCTNotificationHelper = require('react-native').NativeModules.QRCTNotificationHelper;

// 检查原生模块是否可用
const isQRCTNotificationHelperAvailable = QRCTNotificationHelper != null;

/**
 * QNotificationHelper
 */
const QNotificationHelper = {
    /**
     * 显示通知
     * @param {object} params 通知参数
     * @param {function} successCallback 成功回调
     * @param {function} failCallback 失败回调
     */
    showNotification: function (params, successCallback = () => { }, failCallback = () => { }) {
        if (!params) {
            failCallback({
                code: -1,
                msg: "params is empty",
                data: {},
            });
            return;
        }
        
        if (!isQRCTNotificationHelperAvailable) {
            failCallback({
                code: -2,
                msg: "QRCTNotificationHelper module is not available",
                data: {},
            });
            return;
        }
        
        if (!QRCTNotificationHelper.showNotification) {
            failCallback({
                code: -3,
                msg: "showNotification method is not available",
                data: {},
            });
            return;
        }

        try {
            QRCTNotificationHelper.showNotification(params, successCallback, failCallback);
        } catch (error) {
            failCallback({
                code: -4,
                msg: "Failed to show notification: " + error.message,
                data: { error: error.toString() }
            });
        }
    },
};

module.exports = QNotificationHelper;
