/**
 *
 * @providesModule QRNMQTT
 */

'use strict';
const QRNMQTTMsg = require('react-native').NativeModules.QRNMQTT;

// 检查原生模块是否可用
const isQRNMQTTAvailable = QRNMQTTMsg != null;

const QRNMQTT = {

     /**
      * 注册业务模块
      * @param {string} bizCode 业务代码
      * @param {function} callback 回调函数
      */
     registerBizModule: function (bizCode = "", callback = () => { }) {
          if (!isQRNMQTTAvailable) {
               return;
          }
          
          if (QRNMQTTMsg.registerBizModule) {
               QRNMQTTMsg.registerBizModule(bizCode, callback);
          }
     },

     /**
      * 取消注册业务模块
      * @param {string} bizCode 业务代码
      * @param {string} mqtt_event_name MQTT事件名称
      * @param {function} callback 回调函数
      */
     unregisterBizModule: function (bizCode = "", mqtt_event_name = "", callback = () => { }) {
          if (!isQRNMQTTAvailable) {
               return;
          }

          if (QRNMQTTMsg.unregisterBizModule) {
               QRNMQTTMsg.unregisterBizModule(bizCode, mqtt_event_name, callback);
          }
     },
     /**
      * 发布数据
      * @param {object} data 要发布的数据
      * @param {string} bizCode 业务代码
      * @param {function} callback 回调函数
      */
     publishData: function (data = {}, bizCode = "", callback = () => { }) {
          if (!isQRNMQTTAvailable) {
               return;
          }

          if (QRNMQTTMsg.publishData) {
               QRNMQTTMsg.publishData(data, bizCode, callback);
          }
     },
     /**
      * 发布到指定主题
      * @param {string} topic 主题
      * @param {object} data 要发布的数据
      * @param {string} bizCode 业务代码
      * @param {function} callback 回调函数
      */
     publishTopic: function (topic = "", data = {}, bizCode = "", callback = () => { }) {
          if (!isQRNMQTTAvailable) {
               return;
          }

          if (QRNMQTTMsg.publishTopic) {
               QRNMQTTMsg.publishTopic(topic, data, bizCode, callback);
          }
     },
     /**
      * 订阅自定义主题
      * @param {string} topic 要订阅的主题
      * @param {function} callback 回调函数
      */
     subscribeCustomTopic: function (topic = "", callback = () => { }) {
          if (!isQRNMQTTAvailable) {
               return;
          }

          if (QRNMQTTMsg.subscribeCustomTopic) {
               QRNMQTTMsg.subscribeCustomTopic(topic, callback);
          }
     },
};

module.exports = QRNMQTT;
