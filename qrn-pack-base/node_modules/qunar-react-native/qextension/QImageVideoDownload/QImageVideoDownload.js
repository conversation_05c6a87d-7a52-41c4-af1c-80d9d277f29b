/**
 * QImageVideoDownload 下载图片视频相关API
 * @providesModule QRNImageVideoDownload
 */

'use strict';
const { NativeModules } = require('react-native');
const { QRNImageVideoDownload } = NativeModules;

const QImageVideoDownload = {
    multiPicDownloadAndSaveAlbum: function (params, successCallback = () => {}, failCallback = () => {}) {
        if (QRNImageVideoDownload && QRNImageVideoDownload.multiPicDownloadAndSaveAlbum) {
            QRNImageVideoDownload.multiPicDownloadAndSaveAlbum(params, successCallback, failCallback);
        } else {
            failCallback({
                bstatus: {
                    code: -1,
                    des: '当前客户端版本不支持该功能',
                    data: {}
                }
            })
        }
    }
}

module.exports = QImageVideoDownload;