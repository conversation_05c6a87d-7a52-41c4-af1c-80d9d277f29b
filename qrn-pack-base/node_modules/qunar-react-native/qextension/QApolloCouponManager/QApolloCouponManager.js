'use strict';

const QnrApolloCouponManager = require('react-native').NativeModules.QnrApolloCouponManager;
/**
 * Qunar 阿波罗弹窗
 */
const QApolloCouponManager = {
    showLuckMoneyPage: function (personalizedStampData) {
        if (QnrApolloCouponManager && QnrApolloCouponManager.showLuckMoneyPage) {
            try {
                QnrApolloCouponManager.showLuckMoneyPage(personalizedStampData);
            } catch (e) {}
        }
    }
};

module.exports = QApolloCouponManager;
