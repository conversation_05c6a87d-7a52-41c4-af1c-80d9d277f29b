/**
 * @providesModule QLoadingError
 * @flow
 */
'use district'

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {View, StyleSheet, Image, Text, TouchableOpacity, ViewPropTypes} from 'react-native';
import ColorConfig from '../../ColorConfig.js';

/**
 * Qunar骆驼加载失败组件
 *
 * @component QLoadingError
 * @example ./Playground/js/Examples/QLoadingErrorExample.js[1-39]
 * @version >=v1.0.0
 * @description 渲染出一个带问号的骆驼的组件。
 *
 * ![QLoadingError](./images/component-QLoadingError.png)
 */
class QLoadingError extends Component {

    constructor(props) {
        super(props);
    }

    render() {
        let {style, titleText, titleStyle, hintText, hintStyle, buttonText, buttonStyle, buttonTextStyle, onPress, hideTitle, hideHint, hideButton, renderButton} = this.props;

        return (
            <View style={[styles.container, style]}>
                <Image source={{uri: 'https://s.qunarzz.com/q_design_font/images/netError.png'}} style={{width: 250, height: 144}}/>
                {
                    hideTitle ? null : <Text style={[styles.title, titleStyle]}>{titleText}</Text>
                }
                {
                    hideHint ? null : <Text style={[styles.content, hintStyle]}>{hintText}</Text>
                }
                {
                    hideButton ? null : (renderButton ? renderButton() : <TouchableOpacity onPress={onPress}>
                        <View style={[styles.button, buttonStyle]}>
                            <Text numberOfLines={1} style={[styles.buttonContent, buttonTextStyle]}>{buttonText}</Text>
                        </View>
                    </TouchableOpacity>)
                }
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor:'transparent',
        overflow: 'hidden',
    },
    title: {
        marginTop: 12,
        fontSize: 16,
        color: ColorConfig['loading-title'],
        maxWidth: 200,
        textAlign: 'center',
    },
    content: {
        fontSize: 14,
        color: ColorConfig['loading-hint'],
        marginTop: 4,
        maxWidth: 200,
        textAlign: 'center',
    },
    button: {
        height: 32,
        backgroundColor: ColorConfig['blue-btn'],
        marginTop: 20,
        marginLeft: 11,
        marginRight: 11,
        paddingLeft: 20,
        paddingRight: 20,
        borderColor: ColorConfig['blue-btn'],
        borderRadius: 16,
        borderWidth: 2,
        overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center',
    },
    buttonContent: {
        fontSize: 14,
        color: ColorConfig['blue-light'],
    }
});

QLoadingError.defaultProps = {
    titleText: '加载失败了',
    hintText: '请检查网络连接或重试',
    buttonText: '重试',
    hideTitle: false,
    hideHint: false,
    hideButton: false,
};

QLoadingError.propTypes = {
    /**
     * 标题内容
     *
     * @property titleText
	 * @type string
     * @default '获取数据失败'
	 * @description 骆驼下面显示的第一行文字
     */
    titleText: PropTypes.string,
    /**
     * 标题样式
     *
     * @property titleStyle
     * @type Text.propTypes.style
     * @default {fontSize: 14, color: '#212121', marginTop: 8}
     * @description 标题的样式
     *
     * **注意：此属性的如果传入样式，会以merge的形式覆盖默认样式。**
     */
    titleStyle: Text.propTypes.style,
    /**
     * 提示内容
     *
     * @property hintText
	 * @type string
     * @default '请检查一下:网络是否通畅?'
	 * @description 骆驼下面显示的第二行文字
     */
    hintText: PropTypes.string,
    /**
     * 提示样式
     *
     * @property hintStyle
     * @type Text.propTypes.style
     * @default {fontSize: 14, color: '#212121', marginTop: 8}
     * @description 提示内容的样式
     *
     * **注意：此属性的如果传入样式，会以merge的形式覆盖默认样式。**
     */
    hintStyle: Text.propTypes.style,
    /**
     * 按钮内容
     *
     * @property buttonText
	 * @type string
     * @default '重试'
	 * @description 骆驼下面显示的按钮的文字
     */
    buttonText: PropTypes.string,
    /**
     * 按钮样式
     *
     * @property buttonStyle
     * @type ViewPropTypes
     * @default {width: 220, height: 40, backgroundColor: '#00bcd4', marginTop: 8, borderColor: '#00bcd4', borderRadius: 2, borderWidth: 2, justifyContent: 'center', alignItems: 'center'}
     * @description 按钮的样式
     *
     * **注意：此属性的如果传入样式，会以merge的形式覆盖默认样式。**
     */
    buttonStyle: ViewPropTypes.style,
    /**
     * 按钮文本样式
     *
     * @property buttonTextStyle
     * @type Text.propTypes.style
     * @default {fontSize: 18, color: '#ffffff',}
     * @description 按钮文本的样式
     *
     * **注意：此属性的如果传入样式，会以merge的形式覆盖默认样式。**
     */
    buttonTextStyle: Text.propTypes.style,
    /**
     * 隐藏标题
     *
     * @property hideTitle
	 * @type bool
     * @default false
	 * @description 是否隐藏骆驼下面的第一行文字，默认为false不隐藏
     */
    hideTitle: PropTypes.bool,
    /**
     * 隐藏提示
     *
     * @property hideHint
	 * @type bool
     * @default false
	 * @description 是否隐藏骆驼下面的第二行文字，默认为false不隐藏
     */
    hideHint: PropTypes.bool,
    /**
     * 隐藏提示
     *
     * @property hideButton
	 * @type bool
     * @default false
	 * @description 是否隐藏骆驼下面的按钮，默认为false不隐藏
     */
    hideButton: PropTypes.bool,
    /**
     * 渲染按钮方法
     *
     * @property renderButton
	 * @type function
     * @return {element} 用来渲染按钮内容的JSX
	 * @description () => renderable
     *
     * 渲染按钮的方法，用来实现自定义的按钮（可以使用[Button](./component-Button.html)组件来渲染，它提供了更多的配置和更多的状态）
     */
    renderButton: PropTypes.func,
    /**
     * 点击事件
     *
     * @property onPress
	 * @type function
     * @param {event} event 点击事件
	 * @description (event) => void
     *
     * 点击骆驼下面的按钮时调用
     */
    onPress: PropTypes.func,
}

module.exports = QLoadingError;
