import PropTypes from 'prop-types';
import React from 'react';
import { ColorPropType, ViewPropTypes, View , requireNativeComponent} from 'react-native';

// if ViewPropTypes is not defined fall back to View.propType (to support RN < 0.44)
const viewPropTypes = ViewPropTypes || View.propTypes;

const propTypes = {
  ...viewPropTypes,

  /**
   * An array of coordinates to describe the polygon
   */
  coordinates: PropTypes.arrayOf(
    PropTypes.shape({
      /**
       * Latitude/Longitude coordinates
       */
      latitude: PropTypes.number.isRequired,
      longitude: PropTypes.number.isRequired,
    })
  ),

  /**
   * The line width to use for the path.
   */
  lineWidth: PropTypes.number,

  /**
   * The line color to use for the path.
   */
  lineColor: ColorPropType,

};

/**
 * default line color and line width
 */
const defaultProps = {
  lineColor: '#000',
  lineWidth: 1,
};

class QMapPolyline extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      ...props,
    };
  }
  static getDerivedStateFromProps(nextProps, prevState) {
    return nextProps
  }

  setNativeProps(props) {
    this.setState(props);
  }

  render() {
    let MapPolyline;
    const implType = this.state.implType;
    if (implType === 'baidu' && Platform.OS === 'ios') {
      MapPolyline = requireNativeComponent('QRCTBaiduMapPolyline', QMapPolyline);
    } else {
      MapPolyline = requireNativeComponent('QMapPolyline', QMapPolyline);
    }

    return (
      <MapPolyline
        {...this.state}
        ref={ref => {
          this.polyline = ref;
        }}
      />
    );
  }
}

QMapPolyline.propTypes = propTypes;
QMapPolyline.defaultProps = defaultProps;

module.exports = QMapPolyline;
