/**
 *
 * @providesModule QMapMarker
 */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
    StyleSheet,
    requireNativeComponent,
    NativeModules,
    Platform,
    ViewPropTypes,
    findNodeHandle,
} from 'react-native';

import resolveAssetSource from 'react-native/Libraries/Image/resolveAssetSource';
import Commands from './QMapMarkerCommands';

const propTypes = {
    ...ViewPropTypes,

    // TODO(lmr): get rid of these?
    identifier: PropTypes.string,
    // reuseIdentifier: PropTypes.string,

    /**
     * The title of the marker. This is only used if the <Marker /> component has no children that
     * are a `<Callout />`, in which case the default callout behavior will be used, which
     * will show both the `title` and the `description`, if provided.
     */
    title: PropTypes.string,

    /**
     * The description of the marker. This is only used if the <Marker /> component has no children
     * that are a `<Callout />`, in which case the default callout behavior will be used,
     * which will show both the `title` and the `description`, if provided.
     */
    description: PropTypes.string,

    /**
     * A custom image to be used as the marker's icon. Only local image resources are allowed to be
     * used.
     */
    image: PropTypes.any,

    /**
     * Opacity level of view/image based markers
     */
    opacity: PropTypes.number,

    /**
     * If no custom marker view or custom image is provided, the platform default pin will be used,
     * which can be customized by this color. Ignored if a custom marker is being used.
     */
    // pinColor: ColorPropType,

    /**
     * The coordinate for the marker.
     */
    coordinate: PropTypes.shape({
        /**
         * Coordinates for the anchor point of the marker.
         */
        latitude: PropTypes.number.isRequired,
        longitude: PropTypes.number.isRequired,
    }).isRequired,

    /**
     * The offset (in points) at which to display the view.
     *
     * By default, the center point of an annotation view is placed at the coordinate point of the
     * associated annotation. You can use this property to reposition the annotation view as
     * needed. This x and y offset values are measured in points. Positive offset values move the
     * annotation view down and to the right, while negative values move it up and to the left.
     *
     * For android, see the `anchor` prop.
     *
     * @platform ios
     */
    centerOffset: PropTypes.shape({
        /**
         * Offset from the anchor point
         */
        x: PropTypes.number.isRequired,
        y: PropTypes.number.isRequired,
    }),

    /**
     * The offset (in points) at which to place the callout bubble.
     *
     * This property determines the additional distance by which to move the callout bubble. When
     * this property is set to (0, 0), the anchor point of the callout bubble is placed on the
     * top-center point of the marker view’s frame. Specifying positive offset values moves the
     * callout bubble down and to the right, while specifying negative values moves it up and to
     * the left.
     *
     * For android, see the `calloutAnchor` prop.
     */
    calloutOffset: PropTypes.number,

    /**
     * Sets the anchor point for the marker.
     *
     * The anchor specifies the point in the icon image that is anchored to the marker's position
     * on the Earth's surface.
     *
     * The anchor point is specified in the continuous space [0.0, 1.0] x [0.0, 1.0], where (0, 0)
     * is the top-left corner of the image, and (1, 1) is the bottom-right corner. The anchoring
     * point in a W x H image is the nearest discrete grid point in a (W + 1) x (H + 1) grid,
     * obtained by scaling the then rounding. For example, in a 4 x 2 image, the anchor point
     * (0.7, 0.6) resolves to the grid point at (3, 1).
     *
     * For ios, see the `centerOffset` prop.
     *
     * @platform android
     */
    // anchor: PropTypes.shape({
    //   /**
    //    * Offset to the callout
    //    */
    //   x: PropTypes.number.isRequired,
    //   y: PropTypes.number.isRequired,
    // }),

    /**
     * Specifies the point in the marker image at which to anchor the callout when it is displayed.
     * This is specified in the same coordinate system as the anchor. See the `andor` prop for more
     * details.
     *
     * The default is the top middle of the image.
     *
     * For ios, see the `calloutOffset` prop.
     *
     * @platform android
     */
    // calloutAnchor: PropTypes.shape({
    //   /**
    //    * Offset to the callout
    //    */
    //   x: PropTypes.number.isRequired,
    //   y: PropTypes.number.isRequired,
    // }),

    /**
     * Sets whether this marker should be flat against the map true or a billboard facing the
     * camera false.
     *
     * @platform android
     */
    flat: PropTypes.bool,

    draggable: PropTypes.bool,

    // /**
    //  * Sets whether this marker should track view changes true.
    //  *
    //  * @platform ios
    //  */

    // tracksViewChanges: PropTypes.bool,

    // /**
    //  * Sets whether this marker should track view changes in info window true.
    //  *
    //  * @platform ios
    //  */

    // tracksInfoWindowChanges: PropTypes.bool,

    /**
     * Stops Marker onPress events from propagating to and triggering MapView onPress events.
     *
     * @platform ios
     */

    stopPropagation: PropTypes.bool,

    /**
     * Callback that is called when the user presses on the marker
     */
    onPress: PropTypes.func,

    /**
     * Callback that is called when the user selects the marker, before the callout is shown.
     *
     * @platform ios
     */
    onSelect: PropTypes.func,

    /**
     * Callback that is called when the marker is deselected, before the callout is hidden.
     *
     * @platform ios
     */
    onDeselect: PropTypes.func,

    /**
     * Callback that is called when the user taps the callout view.
     */
    onCalloutPress: PropTypes.func,

    /**
     * Callback that is called when the user initiates a drag on this marker (if it is draggable)
     */
    onDragStart: PropTypes.func,

    /**
     * Callback called continuously as the marker is dragged
     */
    onDrag: PropTypes.func,

    /**
     * Callback that is called when a drag on this marker finishes. This is usually the point you
     * will want to setState on the marker's coordinate again
     */
    onDragEnd: PropTypes.func,

    rotate: PropTypes.number,

    imageStyle: PropTypes.shape({
        width: PropTypes.number.isRequired,
        height: PropTypes.number.isRequired,
    }),

    onlyShowIconWithInfoWindow: PropTypes.bool,
};

class QMapMarker extends Component {
    constructor(props) {
        super(props);

        this.state = {
            ...props,
        };

        this.showCallout = this.showCallout.bind(this);
        this.hideCallout = this.hideCallout.bind(this);
        this._onPress = this._onPress.bind(this);
        this._onSelect = this._onSelect.bind(this);
        this._onDeselect = this._onDeselect.bind(this);
        this._onDrag = this._onDrag.bind(this);
        this._onDragStart = this._onDragStart.bind(this);
        this._onDragEnd = this._onDragEnd.bind(this);
        this._onCalloutPress = this._onCalloutPress.bind(this);
    }

    setNativeProps(props) {
        this.setState(props);
    }

    static getDerivedStateFromProps(nextProps, prevState) {
        return nextProps;
    }

    _getHandle() {
        return this.marker;
    }

    getUIManagerCommand(name) {
        return NativeModules.UIManager.QMapMarker.Commands[name];
    }

    getMapManagerCommand(name) {
        return NativeModules[`QMapMarkerManager`][name];
    }

    _runCommand(name, args) {
        Commands[name](this.marker, ...args);
    }

    showCallout() {
        this._runCommand('showCallout', []);
    }

    hideCallout() {
        this._runCommand('hideCallout', []);
    }

    selectCallout() {
        this._runCommand('selectCallout', []);
    }

    deselectCallout() {
        this._runCommand('deselectCallout', []);
    }

    _onPress(event) {
        if (this.state.stopPropagation) {
            event.stopPropagation();
        }

        if (this.state.onPress) {
            this.state.onPress(event.nativeEvent);
        }
    }

    _onCalloutPress(event) {
        if (this.state.stopPropagation) {
            event.stopPropagation();
        }

        if (this.state.onCalloutPress) {
            this.state.onCalloutPress(event.nativeEvent);
        }
    }

    _onSelect(event) {
        if (this.state.onSelect) {
            this.state.onSelect(event.nativeEvent);
        }
    }

    _onDeselect(event) {
        if (this.state.onDeselect) {
            this.state.onDeselect(event.nativeEvent);
        }
    }

    _onDrag(event) {
        if (this.state.onDrag) {
            this.state.onDrag(event.nativeEvent);
        }
    }

    _onDragStart(event) {
        if (this.state.onDragStart) {
            this.state.onDragStart(event.nativeEvent);
        }
    }

    _onDragEnd(event) {
        if (this.state.onDragEnd) {
            this.state.onDragEnd(event.nativeEvent);
        }
    }

    shouldComponentUpdate(nextProps, nextState) {
        if ((Platform.OS === 'android' || (Platform.OS === 'ios' && this.props.implType === 'baidu'))  && nextProps.children !== this.props.children) {
            this._runCommand('updateMarker', []);
        }

        return true;
    }

    render() {
        let image;
        if (this.state.image) {
            image = resolveAssetSource(this.state.image) || {};
            image = image.uri || this.state.image;
        }

        let MapMarker;
        const implType = this.state.implType;
        if (implType === 'baidu' && Platform.OS === 'ios') {
            MapMarker = requireNativeComponent('QRCTBaiduMapMarker', QMapMarker);
        } else {
            MapMarker = requireNativeComponent('QMapMarker', QMapMarker);
        }

        return (
            <MapMarker
                ref={(ref) => {
                    this.marker = ref;
                }}
                {...this.state}
                image={image}
                style={[styles.marker, this.state.style]}
                onPress={this._onPress}
                onSelect={this._onSelect}
                onDeselect={this._onDeselect}
                onDrag={this._onDrag}
                onDragStart={this._onDragStart}
                onDragEnd={this._onDragEnd}
                onCalloutPress={this._onCalloutPress}
            />
        );
    }
}

QMapMarker.propTypes = propTypes;

const styles = StyleSheet.create({
    marker: {
        position: 'absolute',
        backgroundColor: 'transparent',
    },
});

module.exports = QMapMarker;
