/**
 *
 * @providesModule CVParam
 */

'use strict';

import { NativeModules } from 'react-native';
const QRCTCVParam = NativeModules.QRCTCVParam;

const CVParam = {

  /**
   * @param object data
   * @param function callback
   * @param function errorCallback
   **/
  getCVParam: function (data, callback, errorCallback) {
    QRCTCVParam.getCVParam(data, callback, errorCallback)
  }
};

module.exports = CVParam;
