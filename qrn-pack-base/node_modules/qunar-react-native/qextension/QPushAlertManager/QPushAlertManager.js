/**
 *
 * @providesModule QPushAlertManager
 */

"use strict";
const QRCTPushAlertManager = require("react-native").NativeModules
  .QPushAlertManager;

const QPushAlertManager = {
  pushGuideView({
    imgUrl = "",
    type = 0,
    businessType = "",
    origin = "",
    success = () => {},
    fail = () => {},
  }) {
    if (!QRCTPushAlertManager || !QRCTPushAlertManager.pushGuideView) {
      fail(new Error("QPushAlertManager或pushGuideView不存在"));
    }
    try {
      QRCTPushAlertManager.pushGuideView(
        { imgUrl, type, businessType, origin },
        success,
        fail
      );
    } catch (err) {
      fail(new Error(`pushGuideView调用失败:${err}`));
    }
  },
};

module.exports = QPushAlertManager;
