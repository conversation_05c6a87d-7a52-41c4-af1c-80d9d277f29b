/**
 *
 * @providesModule UELog
 */

'use strict';

var Platform = require('react-native').Platform;
var QRNUelog = Platform.OS === 'ios' ? require('react-native').NativeModules.QRNUelog : require('react-native').NativeModules.QRNUeLog;

const QUELog = {
  /**
   * @param {Array<string>} logs
   **/
  log: function (logs) {
    QRNUelog.log(logs);
  },

  /**
   * @param string log
   **/
  logOrigin: function (log) {
    QRNUelog && QRNUelog.logOrigin(log);
  }
};

module.exports = QUELog;
