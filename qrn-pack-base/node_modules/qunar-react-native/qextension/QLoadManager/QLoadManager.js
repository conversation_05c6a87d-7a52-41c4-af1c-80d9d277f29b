'use strict';

const QRCTLoadManager = require('react-native').NativeModules.QRCTLoadManager;
/**
 * 预加载实例
 */
const QLoadManager = {
    preloadBridge: function (hybridId, successCallback = () => {}, failCallback = () => {}) {
        if (QRCTLoadManager && QRCTLoadManager.preloadBridge) {
            try {
                QRCTLoadManager.preloadBridge(hybridId, successCallback, failCallback);
            } catch (e) {
                failCallback(e);
            }
        }
    }
};

module.exports = QLoadManager;
