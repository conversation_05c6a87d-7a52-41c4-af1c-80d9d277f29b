'use strict';

const QRCTAPMHelper = require('react-native').NativeModules.QRCTAPMHelper;
/**
 * QAPMHelper
 */
const QAPMHelper = {
    // 更新pageName
    updatePageName: function (params) {
        if (QRCTAPMHelper && QRCTAPMHelper.updatePageName) {
            const { props = {}, newPageName = null, successCallback = () => {}, failCallback = () => {} } = params;
            const { pageName = null, rootTag = null } = props;

            QRCTAPMHelper.updatePageName(
                { rootTag, originalPageName: pageName, pageName: newPageName },
                successCallback,
                (error) => {
                    failCallback(new Error(error.msg));
                }
            );
        }
    },
};

module.exports = QAPMHelper;
