/**
 * @providesModule QBC
 */
'use strict';

const { QSendNotification, QLoadManager } = require('qunar-react-native');
const { NativeModules } = require('react-native');
const QRCTInferenceManager = NativeModules?.QRCTInferenceManager;
/**
 * 生成唯一ID
 */
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 5);
};

/**
 * QBC - 行为中心
 * 允许业务通过行为中心进行订阅和推理
 */
class QBC {
  // 跨实例通信通道名称
  static CHANNEL_NAME = 'QBehavior-subscriber-channel';

  // 全局初始化事件ID，用于监听全局初始化
  static GLOBAL_INIT_ID = '__global_init__';

  // 全局销毁事件ID，用于监听全局销毁
  static GLOBAL_DESTROY_ID = '__global_destroy__';

  // 消息类型
  static NOTIFY_TYPES = {
    FEEDBACK: 'feedback', // 反馈结果通知
    INFER: 'infer', // 推理结果通知
    INFER_SESSION: 'infer_session', // 推理会话/全局初始化通知
    INFER_TRIGGER: 'infer_trigger', // 触发推理通知
    INFER_DESTROY: 'infer_destroy', // 推理销毁通知
    INIT_RESULT: 'init_result', // 初始化结果通知 (新增)
    DESTROY_RESULT: 'destroy_result' // 销毁结果通知 (新增)
  };

  // 默认为QBC端，只有调用init方法的实例才视为普通端
  static isQBC = true;

  constructor() {
    this.subscriberPools = {
      [QBC.NOTIFY_TYPES.FEEDBACK]: new Map(), // 正负反馈订阅池
      [QBC.NOTIFY_TYPES.INFER]: new Map(), // 推理订阅池
      [QBC.NOTIFY_TYPES.INFER_SESSION]: new Map(), // 推理会话订阅池
      [QBC.NOTIFY_TYPES.INFER_TRIGGER]: new Map(), // 推理触发监听池
      [QBC.NOTIFY_TYPES.INFER_DESTROY]: new Map(), // 推理销毁监听池
      [QBC.NOTIFY_TYPES.INIT_RESULT]: new Map(), // 初始化结果订阅池 (新增)
      [QBC.NOTIFY_TYPES.DESTROY_RESULT]: new Map() // 销毁结果订阅池 (新增)
    };

    // 实例的唯一ID
    this.instanceId = generateId();

    // 初始化跨实例通信通道
    this._setupCrossInstanceChannel();
  }

  /**
   * 验证参数
   * @param {string} sceneId 场景ID
   * @param {function} [callback] 回调函数
   * @param {boolean} requireCallback 是否要求回调必须存在
   * @private
   */
  _validateParams(sceneId, callback, requireCallback = true) {
    if (!sceneId || typeof sceneId !== 'string') {
      throw new Error('场景ID必须是非空字符串');
    }

    // 对全局初始化ID和全局销毁ID不做格式验证
    if (sceneId !== QBC.GLOBAL_INIT_ID && sceneId !== QBC.GLOBAL_DESTROY_ID && !this._validateSceneIdFormat(sceneId)) {
      throw new Error(`场景ID格式错误: ${sceneId}，应为三段式格式，使用下划线分隔："部门_业务_场景"`);
    }

    if (requireCallback && typeof callback !== 'function') {
      throw new Error('回调必须是函数');
    }
  }

  /**
   * 验证场景ID格式
   * @param {string} sceneId 场景ID
   * @returns {boolean} 是否合法
   * @private
   */
  _validateSceneIdFormat(sceneId) {
    // 验证是否为三段式或四段式格式，使用下划线分隔
    const pattern = /^[^_]+_[^_]+_[^_]+(_[^_]+)?$/;

    return pattern.test(sceneId);
  }

  /**
   * 设置跨实例通信通道
   * @private
   */
  _setupCrossInstanceChannel() {
    QSendNotification.addNotification({
      name: QBC.CHANNEL_NAME,
      subscriptionCallback: (subscription) => {
        this.channelSubscription = subscription;
      },
      dataCallback: (message) => {
        this._handleCrossInstanceMessage(message);
      },
      errorCallback: (error) => {
        console.error('跨实例通道错误:', error);
      }
    });
  }

  /**
   * 处理来自其他实例的消息
   * @private
   */
  _handleCrossInstanceMessage(message) {
    const { sourceInstanceId, sceneId, type, isToQBC, data } = message;

    // 忽略自己发出的消息
    if (sourceInstanceId === this.instanceId) {
      return;
    }

    // 如果消息是发送给QBC端的，但当前不是QBC端，则忽略
    if (isToQBC && !QBC.isQBC) {
      return;
    }

    // 根据消息类型通知对应的订阅池
    this._notifySubscribers(type, sceneId, data).catch((err) => console.error(`通知订阅者出错:`, err));
  }

  /**
   * 启动行为中心
   * 调用此方法表示实例是客户端（接收方），非QBC端
   * @param {string} sceneId 场景ID
   * @param {Object} options 初始化选项
   * @param {function} callback 回调函数，接收结果对象
   * @returns {function} 用于取消注册的函数
   */
  init(sceneId, options = {}, callback = () => {}) {
    // 标记当前实例为普通端，非QBC端
    QBC.isQBC = false;

    const unregisterInitResult = this._register(
      QBC.NOTIFY_TYPES.INIT_RESULT,
      sceneId,
      (result) => {
        callback(result || { code: -1, msg: '初始化失败' });
      },
      true
    );

    QLoadManager.preloadBridge(
      'pf_behaviorcenter_rn',
      () => {
        // 发送全局初始化通知 - 使用固定的全局ID
        this._notify(
          QBC.NOTIFY_TYPES.INFER_SESSION,
          QBC.GLOBAL_INIT_ID,
          {
            timestamp: Date.now(),
            sceneId,
            options
          },
          true
        );
      },
      (err) => {
        // 取消初始化结果监听
        unregisterInitResult();
        callback({ code: -1, data: null, msg: `行为中心启动失败: ${err?.msg || JSON.stringify(err)}` });
      }
    );

    return unregisterInitResult;
  }

  /**
   * QBC端专用：监听全局初始化
   * 仅在QBC端使用，监听业务的init()调用
   * @param {function} callback 回调函数，接收包含sceneId的初始化数据
   * @returns {function} 用于取消注册的函数
   */
  onInit(callback) {
    if (!QBC.isQBC) {
      console.warn('此方法仅供QBC端使用');
    }

    // 使用固定的全局ID注册
    return this._register(QBC.NOTIFY_TYPES.INFER_SESSION, QBC.GLOBAL_INIT_ID, callback, false);
  }

  /**
   * 通用注册订阅者方法
   * @param {string} type 订阅类型
   * @param {string} sceneId 场景ID
   * @param {function} callback 回调函数
   * @param {boolean} once 是否为一次性订阅
   * @returns {function} 取消订阅函数
   * @private
   */
  _register(type, sceneId, callback, once = false) {
    this._validateParams(sceneId, callback);

    const pool = this.subscriberPools[type];

    if (!pool.has(sceneId)) {
      pool.set(sceneId, []);
    }

    let finalCallback = callback;

    // 如果是一次性订阅，包装回调函数
    if (once) {
      finalCallback = (data) => {
        this._unregister(type, sceneId, finalCallback);
        return callback(data);
      };
    }

    const subscriber = {
      callback: finalCallback,
      timestamp: Date.now()
    };

    pool.get(sceneId).push(subscriber);

    return () => this._unregister(type, sceneId, finalCallback);
  }

  /**
   * 通用取消注册方法
   * @param {string} type 订阅类型
   * @param {string} sceneId 场景ID
   * @param {function} [callback] 回调函数，不传则取消所有
   * @returns {boolean} 是否成功取消
   * @private
   */
  _unregister(type, sceneId, callback) {
    this._validateParams(sceneId, callback, false);

    const pool = this.subscriberPools[type];

    if (!pool.has(sceneId)) {
      return false;
    }

    const subscribers = pool.get(sceneId);

    if (callback) {
      const initialLength = subscribers.length;
      const filteredSubscribers = subscribers.filter((s) => s.callback !== callback);
      pool.set(sceneId, filteredSubscribers);

      if (filteredSubscribers.length === 0) {
        pool.delete(sceneId);
      }

      return initialLength > filteredSubscribers.length;
    } else {
      return pool.delete(sceneId);
    }
  }

  /**
   * 通用订阅者通知方法
   * @param {string} type 订阅类型
   * @param {string} sceneId 场景ID
   * @param {Object} data 通知数据
   * @returns {Promise<Array<{status: 'fulfilled' | 'rejected', value?: any, reason?: any}>>} 包含所有Promise结果的数组
   * @private
   */
  async _notifySubscribers(type, sceneId, data) {
    const pool = this.subscriberPools[type];

    if (!pool || !pool.has(sceneId)) {
      return [];
    }

    const subscribers = pool.get(sceneId);

    // 创建一个Promise数组，每个Promise都会处理自己的成功或失败状态
    const promiseTasks = subscribers.map((subscriber) => {
      try {
        // 将回调包装在Promise.resolve中，以处理同步和异步回调
        return Promise.resolve(subscriber.callback(data))
          .then((value) => ({ status: 'fulfilled', value: value })) // 成功时返回标准格式
          .catch((reason) => {
            console.error(`场景[${sceneId}]回调执行错误 (异步):`, reason);
            return { status: 'rejected', reason: reason }; // 失败时返回标准格式
          });
      } catch (err) {
        // 捕获同步执行回调时抛出的错误
        console.error(`场景[${sceneId}]回调执行错误 (同步):`, err);
        // 返回一个已rejected的Promise的标准格式
        return Promise.resolve({ status: 'rejected', reason: err });
      }
    });

    // 使用Promise.all等待所有包装后的Promise完成
    // 因为每个Promise现在都会resolve，所以Promise.all不会提前失败
    return Promise.all(promiseTasks);
  }

  /**
   * 通用通知方法
   * @param {string} type 通知类型
   * @param {string} sceneId 场景ID
   * @param {Object} data 通知数据
   * @param {boolean} isToQBC 是否发送给QBC端
   * @private
   */
  _notify(type, sceneId, data, isToQBC = false) {
    this._validateParams(sceneId, null, false);

    if (typeof data !== 'object') {
      throw new Error('数据必须是对象');
    }

    // 通知其他实例
    QSendNotification.sendNotification({
      name: QBC.CHANNEL_NAME,
      data: {
        sourceInstanceId: this.instanceId,
        sceneId,
        type,
        isToQBC,
        data
      }
    });
  }

  /**
   * 注册正负反馈监听
   * @param {string} sceneId 场景ID，格式应为 三段式格式
   * @param {function} callback 回调函数
   * @returns {function} 用于取消注册的函数
   */
  register(sceneId, callback = () => {}) {
    // 注册本地订阅
    return this._register(QBC.NOTIFY_TYPES.FEEDBACK, sceneId, callback, false);
  }

  /**
   * 注册一次性监听，触发后自动取消注册
   * @param {string} sceneId 场景ID，格式应为 三段式格式
   * @param {function} callback 回调函数
   * @returns {function} 用于取消注册的函数
   */
  registerOnce(sceneId, callback) {
    // 注册一次性订阅
    return this._register(QBC.NOTIFY_TYPES.FEEDBACK, sceneId, callback, true);
  }

  /**
   * 反注册正负反馈监听
   * @param {string} sceneId 场景ID，格式应为 三段式格式
   * @param {function} [callback] 指定回调函数，不传则取消所有
   * @returns {boolean} 是否成功取消
   */
  unregister(sceneId, callback) {
    return this._unregister(QBC.NOTIFY_TYPES.FEEDBACK, sceneId, callback);
  }

  /**
   * 执行推理并注册一次性监听
   * @param {string} sceneId 场景ID，格式应为三段式格式
   * @param {Object} options 推理选项
   * @param {function} callback 回调函数
   * @returns {function} 用于取消注册的函数
   */
  infer(sceneId, options = {}, callback) {
    // 注册推理订阅
    const unregisterFn = this._register(QBC.NOTIFY_TYPES.INFER, sceneId, callback, true);

    // 通知QBC端触发推理
    this._notify(
      QBC.NOTIFY_TYPES.INFER_TRIGGER,
      sceneId,
      {
        timestamp: Date.now(),
        options
      },
      true
    );

    return unregisterFn;
  }

  /**
   * QBC端专用：监听推理触发
   * 仅在QBC端使用，监听业务的infer()调用
   * @param {string} sceneId 场景ID，格式应为 三段式格式
   * @param {function} callback 回调函数
   * @returns {function} 用于取消注册的函数
   */
  onInfer(sceneId, callback) {
    if (!QBC.isQBC) {
      console.warn('此方法仅供QBC端使用');
    }
    return this._register(QBC.NOTIFY_TYPES.INFER_TRIGGER, sceneId, callback, false);
  }

  /**
   * 销毁指定场景的推理会话
   * @param {string} sceneId 场景ID，格式应为三段式格式
   * @param {function} callback 回调函数，接收结果对象
   * @returns {function} 用于取消注册的函数
   */
  destroy(sceneId, callback = () => {}) {
    const unregisterDestroyResult = this._register(
      QBC.NOTIFY_TYPES.DESTROY_RESULT,
      sceneId,
      (result) => {
        callback(result || { code: -1, msg: '销毁失败' });
      },
      true
    );

    // 发送销毁通知
    this._notify(
      QBC.NOTIFY_TYPES.INFER_DESTROY,
      QBC.GLOBAL_DESTROY_ID,
      {
        timestamp: Date.now(),
        sceneId
      },
      true
    );

    return unregisterDestroyResult;
  }

  /**
   * QBC端专用：监听推理销毁
   * 仅在QBC端使用，监听业务的destroy()调用
   * @param {function} callback 回调函数
   * @returns {function} 用于取消注册的函数
   */
  onDestroy(callback) {
    if (!QBC.isQBC) {
      console.warn('此方法仅供QBC端使用');
    }

    return this._register(QBC.NOTIFY_TYPES.INFER_DESTROY, QBC.GLOBAL_DESTROY_ID, callback, false);
  }

  /**
   * 行为中心专用通知，对外通知正负反馈计算结果数据
   * @param {string} sceneId 场景ID，格式应为 三段式格式
   * @param {{
   *   value: number   // 正负反馈计算结果数值
   * }} result 通知正负反馈计算结果
   */
  notifyFeedBackResult(sceneId, result) {
    this._notify(QBC.NOTIFY_TYPES.FEEDBACK, sceneId, result);
  }

  /**
   * 行为中心专用通知，对外通知推理结果数据
   * @param {string} sceneId 场景ID，格式应为 三段式格式
   * @param {{
   *   code: number,   // 结果状态码
   *   data: Object,   // 结果数据
   *   msg: string     // 结果消息
   * }} result 推理结果数据
   */
  notifyInferResult(sceneId, result) {
    this._notify(QBC.NOTIFY_TYPES.INFER, sceneId, result);
  }

  /**
   * QBC端专用：通知场景初始化结果
   * @param {string} sceneId 场景ID，格式应为 三段式格式
   * @param {{
   *   code: number,   // 结果状态码
   *   data: Object,   // 结果数据
   *   msg: string     // 结果消息
   * }} result 初始化结果数据
   */
  notifySceneInitResult(sceneId, result) {
    if (!QBC.isQBC) {
      console.warn('此方法仅供QBC端使用');
    }
    this._notify(QBC.NOTIFY_TYPES.INIT_RESULT, sceneId, result);
  }

  /**
   * QBC端专用：通知场景销毁结果
   * @param {string} sceneId 场景ID，格式应为 三段式格式
   * @param {{
   *   code: number,   // 结果状态码
   *   data: Object,   // 结果数据
   *   msg: string     // 结果消息
   * }} result 销毁结果数据
   */
  notifySceneDestroyResult(sceneId, result) {
    if (!QBC.isQBC) {
      console.warn('此方法仅供QBC端使用');
    }
    this._notify(QBC.NOTIFY_TYPES.DESTROY_RESULT, sceneId, result);
  }

  /**
   * 清除所有订阅者
   */
  clearAllSubscribers() {
    Object.values(this.subscriberPools).forEach((pool) => pool.clear());
  }

  /**
   * 发送数据到原生
   * @param {Object} data 要发送的数据对象
   * @returns {void}
   */
  sendData(data) {
    if (Object.prototype.toString.call(data) !== '[object Object]') {
      console.warn('sendData方法参数必须是对象');
      return;
    }

    QRCTInferenceManager?.sendData && QRCTInferenceManager?.sendData(data);
  }
}

module.exports = new QBC();
