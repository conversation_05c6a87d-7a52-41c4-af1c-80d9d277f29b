"use strict";

function generateUUID() {
  var d = new Date().getTime();
  var uuid = "JS-xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (
    c
  ) {
    var r = (d + Math.random() * 16) % 16 | 0;
    d = Math.floor(d / 16);
    return (c == "x" ? r : (r & 0x3) | 0x8).toString(16);
  });
  return uuid;
}

const QSiteLetter = require("react-native").NativeModules.QSiteLetter;
/**
 * qunar 站内信
 */
const QSiteLetterManager = {
  startSiteLetterAuto: function (
    param,
    pageName,
    startCallback,
    successCallback,
    errorCallback
  ) {
    if (QSiteLetter && QSiteLetter.startSiteLetterAuto) {
      try {
        const requestId = generateUUID();
        startCallback(requestId);
        QSiteLetter.startSiteLetterAuto(
          param,
          pageName,
          requestId,
          successCallback,
          errorCallback
        );
      } catch (e) {}
    }
  },
  startSiteLetterManual: function (
    param,
    pageName,
    startCallback,
    successCallback,
    errorCallback
  ) {
    if (QSiteLetter && QSiteLetter.startSiteLetterManual) {
      try {
        const requestId = generateUUID();
        startCallback(requestId);
        QSiteLetter.startSiteLetterManual(
          param,
          pageName,
          requestId,
          successCallback,
          errorCallback
        );
      } catch (e) {}
    }
  },
  showSiteLetter: function (requestId) {
    if (QSiteLetter && QSiteLetter.showSiteLetter) {
      try {
        QSiteLetter.showSiteLetter(requestId);
      } catch (e) {}
    }
  },
  hideSiteLetter: function (requestId) {
    if (QSiteLetter && QSiteLetter.hideSiteLetter) {
      try {
        QSiteLetter.hideSiteLetter(requestId);
      } catch (e) {}
    }
  },
};

module.exports = QSiteLetterManager;
