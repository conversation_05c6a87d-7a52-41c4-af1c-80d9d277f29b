"use strict";
const { NativeModules } = require("react-native");
const QRCTANIManager = NativeModules.QRCTANIManager;

const QANIManager = {
    addAppIntentEntity: function (
        params,
        successCallback = () => { },
        failCallback = () => { }
    ) {
        if (!Array.isArray(params)) {
            failCallback({
                code: -1,
                msg: "params必填且必须为数组"
            });
            return;
        }
        if (QRCTANIManager && QRCTANIManager.addAppIntentEntity) {
            QRCTANIManager.addAppIntentEntity(
                params,
                function successCB(result) {
                    successCallback(result);
                },
                failCallback
            );
        } else {
            failCallback({
                code: -1,
                msg: "当前客户端版本不支持该功能"
            });
        }
    },

    updateAppIntentEntity: function (
        params,
        successCallback = () => { },
        failCallback = () => { }
    ) {
        if (!Array.isArray(params)) {
            failCallback({
                code: -1,
                msg: "params必填且必须为数组"
            });
            return;
        }
        if (QRCTANIManager && QRCTANIManager.updateAppIntentEntity) {
            QRCTANIManager.updateAppIntentEntity(
                params,
                function successCB(result) {
                    successCallback(result);
                },
                failCallback
            );
        } else {
            failCallback({
                code: -1,
                msg: "当前客户端版本不支持该功能"
            });
        }
    },

    removeAppIntentEntity: function (
        params,
        successCallback = () => { },
        failCallback = () => { }
    ) {
        if (!Array.isArray(params)) {
            failCallback({
                code: -1,
                msg: "params必填且必须为数组"
            });
            return;
        }
        if (QRCTANIManager && QRCTANIManager.removeAppIntentEntity) {
            QRCTANIManager.removeAppIntentEntity(
                params,
                function successCB(result) {
                    successCallback(result);
                },
                failCallback
            );
        } else {
            failCallback({
                code: -1,
                msg: "当前客户端版本不支持该功能"
            });
        }
    },
};


module.exports = QANIManager;