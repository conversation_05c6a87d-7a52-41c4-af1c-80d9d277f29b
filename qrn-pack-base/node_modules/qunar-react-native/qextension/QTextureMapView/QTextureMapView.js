/**
 *
 * @providesModule QTextureMapView 仅安卓可用, api和 QMapView 一致
 */
import React from 'react';
const QMapView = require('../QMapView/QMapView');
import { requireNativeComponent } from 'react-native';

class QTextureMapView extends QMapView {
    constructor() {
        super();
    }

    render() {
        let props;

        if (this.state.isReady) {
            props = {
                region: null,
                initialRegion: null,
                coordinateType: 'GCJ02',
                ...this.props,
                onMarkerPress: this._onMarkerPress,
                onMapReady: this._onMapReady,
                onLayout: this._onLayout,
                onMarkerDrag: this._onMarkerDrag,
                onMarkerDragStart: this._onMarkerDragStart,
                onMarkerDragEnd: this._onMarkerDragEnd,
                onPress: this._onPress,
                onLongPress: this._onLongPress,
                onUserLocationChange: this._onUserLocationChange,
                onChange: this._onChange,
                onChangeStart: this._onChangeStart,
            };
        } else {
            props = {
                style: this.props.style,
                region: null,
                initialRegion: null,
                onMarkerPress: this._onMarkerPress,
                onChange: this._onChange,
                onMapReady: this._onMapReady,
                onLayout: this._onLayout,
                onChangeStart: this._onChangeStart,
            };
        }

        return (
            <QRNTextureMapView
                ref={(ref) => {
                    this.map = ref;
                }}
                {...props}
            />
        );
    }
}

const QRNTextureMapView = requireNativeComponent('QTextureMapView', QTextureMapView);

module.exports = QTextureMapView;
