/**
 * QAudioManager 音频录制相关API
 * @providesModule QAudioManager
 */

'use strict';
const { NativeModules } = require('react-native');
const { QRNAudioManager } = NativeModules;

const QAudioManager = {
    requestAudioPermission: function (successCallback = () => { }, failCallback = () => { }) {
        if (QRNAudioManager && QRNAudioManager.requestAudioPermission)
        {
            QRNAudioManager.requestAudioPermission(successCallback, failCallback);
        } else
        {
            failCallback({
                bstatus: {
                    code: -1,
                    des: '当前客户端版本不支持该功能',
                    data: {}
                }
            })
        }
    },
    startRecordWithParam: function (params, successCallback = () => { }, failCallback = () => { }) {
        if (QRNAudioManager && QRNAudioManager.startRecordWithParam)
        {
            QRNAudioManager.startRecordWithParam(params, successCallback, failCallback);
        } else
        {
            failCallback({
                bstatus: {
                    code: -1,
                    des: '当前客户端版本不支持该功能',
                    data: {}
                }
            })
        }
    },
    stopRecorder: function () {
        if (QRNAudioManager && QRNAudioManager.stopRecorder)
        {
            QRNAudioManager.stopRecorder();
        } else
        {
            failCallback({
                bstatus: {
                    code: -1,
                    des: '当前客户端版本不支持该功能',
                    data: {}
                }
            })
        }
    },
    cancelRecorder: function () {
        if (QRNAudioManager && QRNAudioManager.cancelRecorder)
        {
            QRNAudioManager.cancelRecorder();
        } else
        {
            failCallback({
                bstatus: {
                    code: -1,
                    des: '当前客户端版本不支持该功能',
                    data: {}
                }
            })
        }
    },
    deleteFilePath: function (path, successCallback = () => { }, failCallback = () => { }) {
        if (QRNAudioManager && QRNAudioManager.deleteFilePath)
        {
            QRNAudioManager.deleteFilePath(path, successCallback, failCallback);
        } else
        {
            failCallback({
                bstatus: {
                    code: -1,
                    des: '当前客户端版本不支持该功能',
                    data: {}
                }
            })
        }
    }
}

module.exports = QAudioManager;
