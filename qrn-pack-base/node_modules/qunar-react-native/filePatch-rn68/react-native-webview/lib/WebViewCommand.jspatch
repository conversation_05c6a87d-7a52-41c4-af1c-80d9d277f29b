/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow strict-local
 */

// QRN ADD 整个文件是为了适配 Fabric
import codegenNativeCommands from 'react-native/Libraries/Utilities/codegenNativeCommands';


interface NativeCommands {
  +goBack: (
    viewRef: React.ElementRef<ComponentType>,
  ) => void;
  +goForward: (
     viewRef: React.ElementRef<ComponentType>,
   ) => void; 
   +reload: (
    viewRef: React.ElementRef<ComponentType>,
  ) => void;
  +stopLoading: (
     viewRef: React.ElementRef<ComponentType>,
   ) => void;  
   +postMessage: (
    viewRef: React.ElementRef<ComponentType>,
  ) => void;
   +injectJavaScript: (
     viewRef: React.ElementRef<ComponentType>,
   ) => void;
   +loadUrl: (
    viewRef: React.ElementRef<ComponentType>,
  ) => void;
  +requestFocus: (
    viewRef: React.ElementRef<ComponentType>,
  ) => void;
}

export default (codegenNativeCommands<NativeCommands>({
  supportedCommands: [
    'goBack', 'goForward','reload','stopLoading','postMessage','injectJavaScript','loadUrl','requestFocus'
  ],
}): NativeCommands);