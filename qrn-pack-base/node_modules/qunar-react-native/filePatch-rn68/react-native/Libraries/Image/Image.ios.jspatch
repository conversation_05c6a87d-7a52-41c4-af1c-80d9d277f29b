/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

import * as React from 'react';
import StyleSheet from '../StyleSheet/StyleSheet';

import ImageInjection from './ImageInjection';
import ImageAnalyticsTagContext from './ImageAnalyticsTagContext';
import flattenStyle from '../StyleSheet/flattenStyle';
import resolveAssetSource from './resolveAssetSource';

import type { ImageProps as ImagePropsType } from './ImageProps';

import type { ImageStyleProp } from '../StyleSheet/StyleSheet';

import type { RootTag } from 'react-native/Libraries/Types/RootTagTypes';

// QRN ADD
const NativeModules = require('../BatchedBridge/NativeModules');
const requireNativeComponent = require('../ReactNative/requireNativeComponent');
const PropTypes = require('prop-types');
const DeprecatedImagePropType = require('deprecated-react-native-prop-types/DeprecatedImagePropType');
let NativeImage, ImageView, isNewImage;

try {
    NativeImage = require('./NativeImageLoaderIOS').default;

    if (!NativeImage) {
        throw new Error('请使用旧版 image');
    }

    ImageView = require('./ImageViewNativeComponent').default;
    isNewImage = true;
} catch (error) {
    NativeImage = NativeModules.ImageViewManager;
    ImageView = requireNativeComponent('RCTImageView');
    isNewImage = false;
}

const ImageProps = {
    ...DeprecatedImagePropType,
    /**
     * 图片的像素比，约高在高分辨屏幕上约清晰，设置为0时会根据屏幕默认设置
     * 这个参数并不会影响图片显示的位置和内容范围，只会影响用户看到的清晰度
     * 主要用于解决在 iPhone 4s、iPhone6 plus上内存占用过大的问题
     * @type PropTypes.number
     */
    imageScale: PropTypes.number,
    /**
     * Animated webp play or display first frame.
     * @type PropTypes.bool
     */
    animated: PropTypes.bool,
};
// QRN END

function getSize(uri: string, success: (width: number, height: number) => void, failure?: (error: any) => void) {
    // QRN ADD 由于新旧传参不一样, 所以需要判断区分
    if (isNewImage) {
        NativeImage.getSize(uri)
            .then(([width, height]) => success(width, height))
            .catch(
                failure ||
                    function () {
                        console.warn('Failed to get size for image ' + uri);
                    }
            );
    } else {
        NativeImage.getSize(
            uri,
            success,
            failure ||
                function () {
                    console.warn('Failed to get size for image: ' + uri);
                }
        );
    }
    // QRN END
}

function getSizeWithHeaders(
    uri: string,
    headers: { [string]: string, ... },
    success: (width: number, height: number) => void,
    failure?: (error: any) => void
): any {
    // QRN ADD
    if (isNewImage) {
        return NativeImage.getSizeWithHeaders(uri, headers)
            .then(function (sizes) {
                success(sizes.width, sizes.height);
            })
            .catch(
                failure ||
                    function () {
                        console.warn('Failed to get size for image: ' + uri);
                    }
            );
    } else {
        return NativeImage.getSizeWithHeaders({ uri, headers })
            .then(function (sizes) {
                success(sizes.width, sizes.height);
            })
            .catch(
                failure ||
                    function () {
                        console.warn('Failed to get size for image: ' + uri);
                    }
            );
    }
    // QRN End
}

function prefetchWithMetadata(url: string, queryRootName: string, rootTag?: ?RootTag): any {
    if (NativeImage.prefetchImageWithMetadata) {
        // number params like rootTag cannot be nullable before TurboModules is available
        return NativeImage.prefetchImageWithMetadata(
            url,
            queryRootName,
            // NOTE: RootTag type
            // $FlowFixMe[incompatible-call] RootTag: number is incompatible with RootTag
            rootTag ? rootTag : 0
        );
    } else {
        return NativeImage.prefetchImage(url);
    }
}

function prefetch(url: string): any {
    return NativeImage.prefetchImage(url);
}

async function queryCache(urls: Array<string>): Promise<{ [string]: 'memory' | 'disk' | 'disk/memory', ... }> {
    return await NativeImage.queryCache(urls);
}

export type ImageComponentStatics = $ReadOnly<{|
    getSize: typeof getSize,
    getSizeWithHeaders: typeof getSizeWithHeaders,
    prefetch: typeof prefetch,
    prefetchWithMetadata: typeof prefetchWithMetadata,
    queryCache: typeof queryCache,
    resolveAssetSource: typeof resolveAssetSource,
    // QRN Add
    propTypes: typeof ImageProps,
    // QRN END
|}>;

/**
 * A React component for displaying different types of images,
 * including network images, static resources, temporary local images, and
 * images from local disk, such as the camera roll.
 *
 * See https://reactnative.dev/docs/image
 */
let Image = (props: ImagePropsType, forwardedRef) => {
    const source = resolveAssetSource(props.source) || {
        uri: undefined,
        width: undefined,
        height: undefined,
    };

    let sources;
    let style: ImageStyleProp;
    if (Array.isArray(source)) {
        style = flattenStyle([styles.base, props.style]) || {};
        sources = source;
    } else {
        const { width, height, uri } = source;
        style = flattenStyle([{ width, height }, styles.base, props.style]) || {};
        sources = [source];

        if (uri === '') {
            console.warn('source.uri should not be an empty string');
        }
    }

    const resizeMode = props.resizeMode || style.resizeMode || 'cover';
    const tintColor = style.tintColor;

    if (props.src != null) {
        console.warn('The <Image> component requires a `source` property rather than `src`.');
    }

    if (props.children != null) {
        throw new Error(
            'The <Image> component cannot contain children. If you want to render content on top of the image, consider using the <ImageBackground> component or absolute positioning.'
        );
    }

    return (
        <ImageAnalyticsTagContext.Consumer>
            {(analyticTag) => {
                return (
                    <ImageView
                        {...props}
                        ref={forwardedRef}
                        style={style}
                        resizeMode={resizeMode}
                        tintColor={tintColor}
                        source={sources}
                        internal_analyticTag={analyticTag}
                    />
                );
            }}
        </ImageAnalyticsTagContext.Consumer>
    );
};

Image = React.forwardRef<ImagePropsType, React.ElementRef<typeof ImageView>>(Image);

if (ImageInjection.unstable_createImageComponent != null) {
    Image = ImageInjection.unstable_createImageComponent(Image);
}

Image.displayName = 'Image';

/**
 * Retrieve the width and height (in pixels) of an image prior to displaying it.
 *
 * See https://reactnative.dev/docs/image#getsize
 */
/* $FlowFixMe[prop-missing] (>=0.89.0 site=react_native_ios_fb) This comment
 * suppresses an error found when Flow v0.89 was deployed. To see the error,
 * delete this comment and run Flow. */
Image.getSize = getSize;

/**
 * Retrieve the width and height (in pixels) of an image prior to displaying it
 * with the ability to provide the headers for the request.
 *
 * See https://reactnative.dev/docs/image#getsizewithheaders
 */
/* $FlowFixMe[prop-missing] (>=0.89.0 site=react_native_ios_fb) This comment
 * suppresses an error found when Flow v0.89 was deployed. To see the error,
 * delete this comment and run Flow. */
Image.getSizeWithHeaders = getSizeWithHeaders;

/**
 * Prefetches a remote image for later use by downloading it to the disk
 * cache.
 *
 * See https://reactnative.dev/docs/image#prefetch
 */
/* $FlowFixMe[prop-missing] (>=0.89.0 site=react_native_ios_fb) This comment
 * suppresses an error found when Flow v0.89 was deployed. To see the error,
 * delete this comment and run Flow. */
Image.prefetch = prefetch;

/**
 * Prefetches a remote image for later use by downloading it to the disk
 * cache, and adds metadata for queryRootName and rootTag.
 *
 * See https://reactnative.dev/docs/image#prefetch
 */
/* $FlowFixMe[prop-missing] (>=0.89.0 site=react_native_ios_fb) This comment
 * suppresses an error found when Flow v0.89 was deployed. To see the error,
 * delete this comment and run Flow. */
Image.prefetchWithMetadata = prefetchWithMetadata;

/**
 * Performs cache interrogation.
 *
 *  See https://reactnative.dev/docs/image#querycache
 */
/* $FlowFixMe[prop-missing] (>=0.89.0 site=react_native_ios_fb) This comment
 * suppresses an error found when Flow v0.89 was deployed. To see the error,
 * delete this comment and run Flow. */
Image.queryCache = queryCache;

/**
 * Resolves an asset reference into an object.
 *
 * See https://reactnative.dev/docs/image#resolveassetsource
 */
/* $FlowFixMe[prop-missing] (>=0.89.0 site=react_native_ios_fb) This comment
 * suppresses an error found when Flow v0.89 was deployed. To see the error,
 * delete this comment and run Flow. */
Image.resolveAssetSource = resolveAssetSource;

// QRN Add
Image.propTypes = ImageProps;
// QRN END

/**
 * Switch to `deprecated-react-native-prop-types` for compatibility with future
 * releases. This is deprecated and will be removed in the future.
 */
Image.propTypes = require('deprecated-react-native-prop-types').ImagePropTypes;

const styles = StyleSheet.create({
    base: {
        overflow: 'hidden',
    },
});

module.exports = ((Image: any): React.AbstractComponent<ImagePropsType, React.ElementRef<typeof ImageView>> &
    ImageComponentStatics);
