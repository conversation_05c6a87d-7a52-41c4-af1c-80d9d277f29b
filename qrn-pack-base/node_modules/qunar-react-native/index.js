import { NativeModules, Platform } from 'react-native';

const QReactExtension = {
    get Button() {
        return require('./extension/Button/Button');
    },
    get CameraRoll() {
        return require('./extension/CameraRoll/CameraRoll');
    },
    get Checked() {
        return require('./extension/Checked/Checked');
    },
    get CookieManager() {
        return require('./extension/CookieManager/CookieManager');
    },
    get DeviceInfo() {
        return require('./extension/DeviceInfo/DeviceInfo.js');
    },
    get EventEmitter() {
        return require('react-native/Libraries/vendor/emitter/EventEmitter.js').default;
    },
    get FontLoader() {
        return require('./extension/FontLoader/FontLoader');
    },
    get ImageUploader() {
        return require('./extension/ImageUploader/ImageUploader');
    },
    get InfiniteListView() {
        return require('./extension/ListView/InfiniteListView');
    },
    get ListView() {
        return require('./extension/ListView/ListView');
    },
    get LoadControl() {
        return require('./extension/LoadControl/LoadControl');
    },
    get Loading() {
        return require('./extension/Loading/Loading');
    },
    get MapUtils() {
        return require('./extension/MapUtils/MapUtils');
    },
    get Modal() {
        return require('./extension/Modal/Modal.js');
    },
    get Picker() {
        return require('./extension/Picker/Picker');
    },
    get ProgressView() {
        return require('./extension/ProgressView/ProgressView');
    },
    get QPInfoManager() {
        return require('./extension/QPInfoManager/QPInfoManager.js');
    },
    get Radio() {
        return require('./extension/Radio/Radio');
    },
    get RefreshControl() {
        // RN 68 版本已经废弃
        return require('./extension/RefreshControl/RefreshControl');
    },
    get ScrollView() {
        if (global.__isFabric === true) {
            // qrn 内部使用的 ScrollView 已废弃, 请使用 react-native 自带的 ScrollView
            // RN 68 版本已经废弃
            return require('react-native/Libraries/Components/ScrollView/ScrollView');
        } else {
            return require('./extension/ScrollView/ScrollView');
        }
    },
    get Slider() {
        return require('./extension/Slider/Slider');
    },
    get StatusBar() {
        return require('./extension/StatusBar/StatusBar.js');
    },
    get Switch() {
        return require('./extension/Switch/Switch');
    },
    get Tab() {
        return require('./extension/Tab/Tab');
    },
    get TabBar() {
        return require('./extension/TabBar/TabBar');
    },
    get TextInput() {
        // RN 68 版本已经废弃
        return require('./extension/TextInput/TextInput');
    },
    get TimePicker() {
        return require('./extension/TimePicker/TimePicker');
    },
    get Toast() {
        return require('./extension/Toast/Toast');
    },
    get TouchableCustomFeedback() {
        return require('./extension/TouchableCustomFeedback/TouchableCustomFeedback');
    },
    get QSendNotification() {
        return require('./extension/QSendNotification/QSendNotification');
    },
    get QLinearGradient() {
        return require('./extension/QLinearGradient/QLinearGradient');
    },
    get FileUploader() {
        return require('./extension/FileUploader/FileUploader');
    },
    get VideoCompresser() {
        return require('./extension/FileUploader/VideoCompresser');
    },
    get QChooseContact() {
        return require('./extension/QChooseContact/QChooseContact');
    },
    get MeasureText() {
        return require('./extension/MeasureText/MeasureText');
    },
    get QAppState() {
        return require('./extension/QAppState/QAppState');
    },
    get AppInfo() {
        return require('./extension/AppInfo/AppInfo');
    },
    get QVideo() {
        return require('./extension/QVideo/QVideo');
    },
    get QVideoControl() {
        return require('./extension/QVideo/QVideoPlayerControl');
    },

    /////////////////////////////////
    /*
     * 实验阶段API和组件
     *
     * 由于组件处于实验阶段,因此在每次发版本时可能会出现大的变化
     * 如果你使用了这些组件请务必让我们知道,这样在组件变化时方便我们通知你
     */
    // get NativeListView() { return require('./extension/ListView/NativeListView'); },
    /////////////////////////////////

    // 大客户端特有API和组件
    get QABTest() {
        return require('./qextension/QABTest/QABTest');
    },
    get QCVParam() {
        return require('./qextension/QCVParam/QCVParam');
    },
    get QHotDogNetWork() {
        return require('./qextension/QHotDogNetWork/QHotDogNetWork');
    },
    get QLoading() {
        return require('./qextension/QLoading/QLoading');
    },
    get QLoadingError() {
        return require('./qextension/QLoadingError/QLoadingError');
    },
    get QLoginManager() {
        return require('./qextension/QLoginManager/QLoginManager');
    },
    get QScreenshotShare() {
        return require('./qextension/QScreenshotShare/QScreenshotShare');
    },
    get QShare() {
        return require('./qextension/QShare/QShare');
    },
    get QPermission() {
        return require('./qextension/QPermission/QPermission');
    },
    get QUELog() {
        return require('./qextension/QUELog/QUELog');
    },
    get QUrs() {
        return require('./qextension/QUrs/QUrs');
    },
   get QRNMQTT() {
           return require('./qextension/QMQTTMsg/QMQTTMsg');
       },
    get QAudioManager() {
        return require('./qextension/QAudio/QAudioManager');
    },
    get QAV() {
        return require('./qextension/QAV/QAV');
    },
    get QRiskControlInfo() {
        return require('./qextension/QRiskControlInfo/QRiskControlInfo');
    },
    get QChooseImage() {
        return require('./qextension/QChooseImage/QChooseImage');
    },
    // get QWaveView() { return require('./qextension/Qmi/QWaveView'); },
    // get Qmi() { return require('./qextension/Qmi/Qmi'); },
    // get QMicView() { return require('./qextension/Qmi/QMicView'); },
    get QMapView() {
        return require('./qextension/QMapView/QMapView');
    },
    get QMapMarker() {
        return require('./qextension/QMapView/QMapMarker');
    },
    get QMapCallout() {
        return require('./qextension/QMapView/QMapCallout');
    },
    get QMapPolyline() {
        return require('./qextension/QMapView/QMapPolyline');
    },
    get QBaiduMapView() {
        return require('./qextension/QMapView/QBaiduMapView/QBaiduMapView');
    },
    get QBaiduMapMarker() {
        return require('./qextension/QMapView/QBaiduMapView/QBaiduMapMarker');
    },
    get QBaiduMapCallout() {
        return require('./qextension/QMapView/QMapCallout');
    },
    get QBaiduMapPolyline() {
        return require('./qextension/QMapView/QBaiduMapView/QBaiduMapPolyline');
    },
    get QVideoPlayer() {
        return require('./qextension/QVideoPlayer/QVideoPlayer');
    },
    get QLocation() {
        return require('./qextension/QLocation/QLocation');
    },
    get QMiniProgram() {
        return require('./qextension/QMiniProgram/QMiniProgram');
    },
    get QSchemeForResult() {
        return require('./qextension/QSchemeForResult/QSchemeForResult');
    },
    get Pay() {
        return require('./qextension/Pay/Pay');
    },
    get WebView() {
        return require('./qextension/QWebView/WebView');
    },
    get NativeNavigator() {
        return require('./extension/NativeNavigator/NativeNavigator');
    },
    get VerifyDialog() {
        return require('./qextension/QVerifyDialog/QVerifyDialog');
    },
    // get QLoadingManager() {
    //     return require('./qextension/QLoadingManager/QLoadingManager');
    // },
    get QLoadManager() {
        return require('./qextension/QLoadManager/QLoadManager');
    },
    get QSiteLetterManager() {
        return require('./qextension/QSiteLetter/QSiteLetter');
    },
    get QApolloCouponManager() {
        return require('./qextension/QApolloCouponManager/QApolloCouponManager');
    },
    get QTextureMapView() {
        if (Platform.OS === 'android') {
            return require('./qextension/QTextureMapView/QTextureMapView');
        } else {
            return require('./qextension/QMapView/QMapView');
        }
    },
    get QAppStoreReview() {
        return require('./qextension/QAppStoreReview/QAppStoreReview');
    },
    get QCommonConfig() {
        return require('./qextension/QCommonConfig/QCommonConfig');
    },
    get QRecoveryData() {
        return require('./qextension/QRecoveryData/QRecoveryData');
    },
    get QCacheRiskControl() {
        return require('./qextension/QCacheRiskControl/QCacheRiskControl');
    },
    get QExpose() {
        return require('./qextension/QExpose/QExpose');
    },
    get QAPMHelper() {
        return require('./qextension/QAPMHelper/QAPMHelper');
    },
    get QUti() {
        return require('./qextension/QUti/QUti');
    },
    get QPushAlertManager() {
        return require('./qextension/QPushAlertManager/QPushAlertManager');
    },
    get QExceptionsManager() {
        return require('./qextension/QExceptionsManager/QExceptionsManager');
    },
    get QRNMercuryBannerAD() {
        return require('./qextension/QBannerAD/QRNMercuryBannerAD');
    },
    get QImageVideoDownload() {
        return require('./qextension/QImageVideoDownload/QImageVideoDownload');
    },
    get QFoldScreenManager() {
        return require('./qextension/FoldScreen/FoldScreen');
    },
    get QInferenceManager() {
        return require('./qextension/QInference/QInferenceManager.js');
    },
    get QURLProtocolManager() {
        return require('./qextension/QURLProtocolManager/QURLProtocolManager.js');
    },
    get QBC() {
        return require('./qextension/QBC/QBC.js');
    },
    get QANIManager() {
        return require('./qextension/QInference/QANIManager.js');
    },
    get QNotificationHelper() {
        return require('./qextension/QNotificationHelper/QNotificationHelper.js');
    },
};

// Patch RN
import './patch/index';

// 设置 native 全局事件监听
import './utils/setNativeListener';

// polyfill 里面的 Object.assign 不支持 getter/setter
let QunarReactNative = {};
[QReactExtension].map((module) => {
    for (let prop in module) {
        if (module.hasOwnProperty(prop)) {
            Object.defineProperty(
                QunarReactNative,
                prop,
                Object.getOwnPropertyDescriptor(module, prop)
            );
        }
    }
});

if (!window._qrct) {
    window._qrct = {};
}

// 预引用, 防止被 tree-shaking
require('./utils/preRequire')();
// 加载 platform 字体
require('./extension/FontLoader/FontLoader');
// 预请求 package.json 中的 extensionDependencies
// import './utils/extensionDependencies';

// 获取新上线功能的开关
NativeModules.QRCTJSCommonConfig &&
    NativeModules.QRCTJSCommonConfig.getQrnJsConfig &&
    NativeModules.QRCTJSCommonConfig.getQrnJsConfig((config) => {
        global.QrnConfig = config;
    });

// 大图内置域名白名单
global.__qrn_internal_qAutoZoomWhiteMap = require('./utils/qAutoZoomWhiteMap');

// 是否使用 qrn 的 measure, 默认使用 RN 的, 需要业务线自己设置
QunarReactNative['SetUseQrnMeasure'] = function (useQrnMeasure) {
    global.__qrn_use_qrn_measure = useQrnMeasure;
};
import './utils/extensionDependencies';

module.exports = QunarReactNative;
